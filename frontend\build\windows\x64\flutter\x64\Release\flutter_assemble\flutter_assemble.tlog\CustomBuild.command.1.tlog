^E:\GITHUB\LOCAL_UPLOAD\FRONTEND\BUILD\WINDOWS\X64\CMAKEFILES\48E49F6F8953C70182BE15296D1BC95C\FLUTTER_WINDOWS.DLL.RULE
setlocal
"E:\Program Files\Microsoft Visual Studio\2022\Community\Common7\IDE\CommonExtensions\Microsoft\CMake\CMake\bin\cmake.exe" -E env FLUTTER_ROOT=E:\flutter PROJECT_DIR=E:\github\local_upload\frontend FLUTTER_ROOT=E:\flutter FLUTTER_EPHEMERAL_DIR=E:\github\local_upload\frontend\windows\flutter\ephemeral PROJECT_DIR=E:\github\local_upload\frontend FLUTTER_TARGET=lib\main.dart DART_DEFINES=RkxVVFRFUl9WRVJTSU9OPTMuMzIuNg==,RkxVVFRFUl9DSEFOTkVMPXN0YWJsZQ==,RkxVVFRFUl9HSVRfVVJMPWh0dHBzOi8vZ2l0aHViLmNvbS9mbHV0dGVyL2ZsdXR0ZXIuZ2l0,RkxVVFRFUl9GUkFNRVdPUktfUkVWSVNJT049MDc3YjRhNGNlMQ==,RkxVVFRFUl9FTkdJTkVfUkVWSVNJT049NzJmMmIxOGJiMA==,RkxVVFRFUl9EQVJUX1ZFUlNJT049My44LjE= DART_OBFUSCATION=false TRACK_WIDGET_CREATION=true TREE_SHAKE_ICONS=true PACKAGE_CONFIG=E:\github\local_upload\frontend\.dart_tool\package_config.json E:/flutter/packages/flutter_tools/bin/tool_backend.bat windows-x64 Release
if %errorlevel% neq 0 goto :cmEnd
:cmEnd
endlocal & call :cmErrorLevel %errorlevel% & goto :cmDone
:cmErrorLevel
exit /b %1
:cmDone
if %errorlevel% neq 0 goto :VCEnd
^E:\GITHUB\LOCAL_UPLOAD\FRONTEND\BUILD\WINDOWS\X64\CMAKEFILES\087F9F2FBB10120D2F1B1BAA05576B02\FLUTTER_ASSEMBLE.RULE
setlocal
:cmEnd
endlocal & call :cmErrorLevel %errorlevel% & goto :cmDone
:cmErrorLevel
exit /b %1
:cmDone
if %errorlevel% neq 0 goto :VCEnd
^E:\GITHUB\LOCAL_UPLOAD\FRONTEND\WINDOWS\FLUTTER\CMAKELISTS.TXT
setlocal
"E:\Program Files\Microsoft Visual Studio\2022\Community\Common7\IDE\CommonExtensions\Microsoft\CMake\CMake\bin\cmake.exe" -SE:/github/local_upload/frontend/windows -BE:/github/local_upload/frontend/build/windows/x64 --check-stamp-file E:/github/local_upload/frontend/build/windows/x64/flutter/CMakeFiles/generate.stamp
if %errorlevel% neq 0 goto :cmEnd
:cmEnd
endlocal & call :cmErrorLevel %errorlevel% & goto :cmDone
:cmErrorLevel
exit /b %1
:cmDone
if %errorlevel% neq 0 goto :VCEnd
