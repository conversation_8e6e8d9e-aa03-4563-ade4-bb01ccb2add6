^E:\GITHUB\LOCAL_UPLOAD\FRONTEND\WINDOWS\RUNNER\CMAKELISTS.TXT
setlocal
"E:\Program Files\Microsoft Visual Studio\2022\Community\Common7\IDE\CommonExtensions\Microsoft\CMake\CMake\bin\cmake.exe" -SE:/github/local_upload/frontend/windows -BE:/github/local_upload/frontend/build/windows/x64 --check-stamp-file E:/github/local_upload/frontend/build/windows/x64/runner/CMakeFiles/generate.stamp
if %errorlevel% neq 0 goto :cmEnd
:cmEnd
endlocal & call :cmErrorLevel %errorlevel% & goto :cmDone
:cmErrorLevel
exit /b %1
:cmDone
if %errorlevel% neq 0 goto :VCEnd
