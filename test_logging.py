#!/usr/bin/env python3
# -*- coding: utf-8 -*-

import os
import sys
import logging
from datetime import datetime
from logging.handlers import RotatingFileHandler

def get_app_data_dir():
    """获取应用程序数据目录"""
    app_name = "TelegramUploader"

    if sys.platform == "win32":
        # Windows: 使用 AppData/Local
        base_dir = os.environ.get('LOCALAPPDATA', os.path.expanduser('~'))
        return os.path.join(base_dir, app_name)
    elif sys.platform == "darwin":
        # macOS: 使用 Application Support
        return os.path.expanduser(f'~/Library/Application Support/{app_name}')
    else:
        # Linux: 使用 .local/share
        return os.path.expanduser(f'~/.local/share/{app_name}')

def setup_logging():
    # 初始化应用数据目录
    app_data_dir = get_app_data_dir()
    os.makedirs(app_data_dir, exist_ok=True)

    # 配置日志 - 写入到程序目录下
    log_dir = os.path.join(app_data_dir, "logs")
    os.makedirs(log_dir, exist_ok=True)

    # 创建日志文件路径
    log_file = os.path.join(log_dir, f"telegram_uploader_{datetime.now().strftime('%Y%m%d')}.log")

    # 清除现有的处理器
    root_logger = logging.getLogger()
    for handler in root_logger.handlers[:]:
        root_logger.removeHandler(handler)

    # 配置根日志记录器
    logging.basicConfig(
        level=logging.INFO,
        format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
        datefmt='%Y-%m-%d %H:%M:%S',
        handlers=[
            # 文件处理器 - 写入日志文件，最大10MB，保留5个备份文件
            RotatingFileHandler(
                log_file, 
                maxBytes=10*1024*1024,  # 10MB
                backupCount=5,
                encoding='utf-8'
            ),
            # 控制台处理器 - 输出到控制台
            logging.StreamHandler()
        ],
        force=True  # 强制重新配置
    )

    logger = logging.getLogger(__name__)
    
    # 记录日志配置信息
    logger.info(f"日志目录: {log_dir}")
    logger.info(f"日志文件: {log_file}")
    logger.info("日志系统初始化完成")
    
    return log_dir, log_file

if __name__ == "__main__":
    print("测试日志配置...")
    log_dir, log_file = setup_logging()
    
    logger = logging.getLogger(__name__)
    
    # 测试各种级别的日志
    logger.debug("这是一个调试消息")
    logger.info("这是一个信息消息")
    logger.warning("这是一个警告消息")
    logger.error("这是一个错误消息")
    
    print(f"日志文件路径: {log_file}")
    print("请检查日志文件是否有内容")
