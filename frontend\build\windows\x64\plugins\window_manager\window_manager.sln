﻿
Microsoft Visual Studio Solution File, Format Version 12.00
# Visual Studio Version 17
Project("{8BC9CEB8-8B4A-11D0-8D11-00A0C91BC942}") = "ALL_BUILD", "ALL_BUILD.vcxproj", "{E76C3D71-5F07-3220-9006-1D8710725D73}"
	ProjectSection(ProjectDependencies) = postProject
		{391C6CE4-8939-3867-A1A5-D2E4CEF587B4} = {391C6CE4-8939-3867-A1A5-D2E4CEF587B4}
		{AAC3A724-1717-3171-8B72-BF156847D8DB} = {AAC3A724-1717-3171-8B72-BF156847D8DB}
	EndProjectSection
EndProject
Project("{8BC9CEB8-8B4A-11D0-8D11-00A0C91BC942}") = "INSTALL", "INSTALL.vcxproj", "{28E938D9-5DE5-3671-AC30-1B3C045F93CB}"
	ProjectSection(ProjectDependencies) = postProject
		{E76C3D71-5F07-3220-9006-1D8710725D73} = {E76C3D71-5F07-3220-9006-1D8710725D73}
		{391C6CE4-8939-3867-A1A5-D2E4CEF587B4} = {391C6CE4-8939-3867-A1A5-D2E4CEF587B4}
	EndProjectSection
EndProject
Project("{8BC9CEB8-8B4A-11D0-8D11-00A0C91BC942}") = "ZERO_CHECK", "..\..\\ZERO_CHECK.vcxproj", "{391C6CE4-8939-3867-A1A5-D2E4CEF587B4}"
	ProjectSection(ProjectDependencies) = postProject
	EndProjectSection
EndProject
Project("{8BC9CEB8-8B4A-11D0-8D11-00A0C91BC942}") = "flutter_assemble", "..\..\flutter\flutter_assemble.vcxproj", "{5DD86A62-ADBC-3EBA-ABFA-8C3E319ECBD5}"
	ProjectSection(ProjectDependencies) = postProject
		{391C6CE4-8939-3867-A1A5-D2E4CEF587B4} = {391C6CE4-8939-3867-A1A5-D2E4CEF587B4}
	EndProjectSection
EndProject
Project("{8BC9CEB8-8B4A-11D0-8D11-00A0C91BC942}") = "flutter_wrapper_plugin", "..\..\flutter\flutter_wrapper_plugin.vcxproj", "{32AAA8A5-8AD3-344A-AB9F-407C0BE20D66}"
	ProjectSection(ProjectDependencies) = postProject
		{391C6CE4-8939-3867-A1A5-D2E4CEF587B4} = {391C6CE4-8939-3867-A1A5-D2E4CEF587B4}
		{5DD86A62-ADBC-3EBA-ABFA-8C3E319ECBD5} = {5DD86A62-ADBC-3EBA-ABFA-8C3E319ECBD5}
	EndProjectSection
EndProject
Project("{8BC9CEB8-8B4A-11D0-8D11-00A0C91BC942}") = "window_manager_plugin", "window_manager_plugin.vcxproj", "{AAC3A724-1717-3171-8B72-BF156847D8DB}"
	ProjectSection(ProjectDependencies) = postProject
		{391C6CE4-8939-3867-A1A5-D2E4CEF587B4} = {391C6CE4-8939-3867-A1A5-D2E4CEF587B4}
		{5DD86A62-ADBC-3EBA-ABFA-8C3E319ECBD5} = {5DD86A62-ADBC-3EBA-ABFA-8C3E319ECBD5}
		{32AAA8A5-8AD3-344A-AB9F-407C0BE20D66} = {32AAA8A5-8AD3-344A-AB9F-407C0BE20D66}
	EndProjectSection
EndProject
Global
	GlobalSection(SolutionConfigurationPlatforms) = preSolution
		Debug|x64 = Debug|x64
		Profile|x64 = Profile|x64
		Release|x64 = Release|x64
	EndGlobalSection
	GlobalSection(ProjectConfigurationPlatforms) = postSolution
		{E76C3D71-5F07-3220-9006-1D8710725D73}.Debug|x64.ActiveCfg = Debug|x64
		{E76C3D71-5F07-3220-9006-1D8710725D73}.Debug|x64.Build.0 = Debug|x64
		{E76C3D71-5F07-3220-9006-1D8710725D73}.Profile|x64.ActiveCfg = Profile|x64
		{E76C3D71-5F07-3220-9006-1D8710725D73}.Profile|x64.Build.0 = Profile|x64
		{E76C3D71-5F07-3220-9006-1D8710725D73}.Release|x64.ActiveCfg = Release|x64
		{E76C3D71-5F07-3220-9006-1D8710725D73}.Release|x64.Build.0 = Release|x64
		{28E938D9-5DE5-3671-AC30-1B3C045F93CB}.Debug|x64.ActiveCfg = Debug|x64
		{28E938D9-5DE5-3671-AC30-1B3C045F93CB}.Profile|x64.ActiveCfg = Profile|x64
		{28E938D9-5DE5-3671-AC30-1B3C045F93CB}.Release|x64.ActiveCfg = Release|x64
		{391C6CE4-8939-3867-A1A5-D2E4CEF587B4}.Debug|x64.ActiveCfg = Debug|x64
		{391C6CE4-8939-3867-A1A5-D2E4CEF587B4}.Debug|x64.Build.0 = Debug|x64
		{391C6CE4-8939-3867-A1A5-D2E4CEF587B4}.Profile|x64.ActiveCfg = Profile|x64
		{391C6CE4-8939-3867-A1A5-D2E4CEF587B4}.Profile|x64.Build.0 = Profile|x64
		{391C6CE4-8939-3867-A1A5-D2E4CEF587B4}.Release|x64.ActiveCfg = Release|x64
		{391C6CE4-8939-3867-A1A5-D2E4CEF587B4}.Release|x64.Build.0 = Release|x64
		{5DD86A62-ADBC-3EBA-ABFA-8C3E319ECBD5}.Debug|x64.ActiveCfg = Debug|x64
		{5DD86A62-ADBC-3EBA-ABFA-8C3E319ECBD5}.Debug|x64.Build.0 = Debug|x64
		{5DD86A62-ADBC-3EBA-ABFA-8C3E319ECBD5}.Profile|x64.ActiveCfg = Profile|x64
		{5DD86A62-ADBC-3EBA-ABFA-8C3E319ECBD5}.Profile|x64.Build.0 = Profile|x64
		{5DD86A62-ADBC-3EBA-ABFA-8C3E319ECBD5}.Release|x64.ActiveCfg = Release|x64
		{5DD86A62-ADBC-3EBA-ABFA-8C3E319ECBD5}.Release|x64.Build.0 = Release|x64
		{32AAA8A5-8AD3-344A-AB9F-407C0BE20D66}.Debug|x64.ActiveCfg = Debug|x64
		{32AAA8A5-8AD3-344A-AB9F-407C0BE20D66}.Debug|x64.Build.0 = Debug|x64
		{32AAA8A5-8AD3-344A-AB9F-407C0BE20D66}.Profile|x64.ActiveCfg = Profile|x64
		{32AAA8A5-8AD3-344A-AB9F-407C0BE20D66}.Profile|x64.Build.0 = Profile|x64
		{32AAA8A5-8AD3-344A-AB9F-407C0BE20D66}.Release|x64.ActiveCfg = Release|x64
		{32AAA8A5-8AD3-344A-AB9F-407C0BE20D66}.Release|x64.Build.0 = Release|x64
		{AAC3A724-1717-3171-8B72-BF156847D8DB}.Debug|x64.ActiveCfg = Debug|x64
		{AAC3A724-1717-3171-8B72-BF156847D8DB}.Debug|x64.Build.0 = Debug|x64
		{AAC3A724-1717-3171-8B72-BF156847D8DB}.Profile|x64.ActiveCfg = Profile|x64
		{AAC3A724-1717-3171-8B72-BF156847D8DB}.Profile|x64.Build.0 = Profile|x64
		{AAC3A724-1717-3171-8B72-BF156847D8DB}.Release|x64.ActiveCfg = Release|x64
		{AAC3A724-1717-3171-8B72-BF156847D8DB}.Release|x64.Build.0 = Release|x64
	EndGlobalSection
	GlobalSection(ExtensibilityGlobals) = postSolution
		SolutionGuid = {6406EBC5-DA19-3DE5-80AD-7ED36B9E8213}
	EndGlobalSection
	GlobalSection(ExtensibilityAddIns) = postSolution
	EndGlobalSection
EndGlobal
