E:\github\local_upload\frontend\windows\flutter\ephemeral\.plugin_symlinks\system_tray\windows\system_tray_plugin.cpp;E:\github\local_upload\frontend\build\windows\x64\plugins\system_tray\system_tray_plugin.dir\Release\system_tray_plugin.obj
E:\github\local_upload\frontend\windows\flutter\ephemeral\.plugin_symlinks\system_tray\windows\utils.cpp;E:\github\local_upload\frontend\build\windows\x64\plugins\system_tray\system_tray_plugin.dir\Release\utils.obj
E:\github\local_upload\frontend\windows\flutter\ephemeral\.plugin_symlinks\system_tray\windows\errors.cpp;E:\github\local_upload\frontend\build\windows\x64\plugins\system_tray\system_tray_plugin.dir\Release\errors.obj
E:\github\local_upload\frontend\windows\flutter\ephemeral\.plugin_symlinks\system_tray\windows\app_window.cpp;E:\github\local_upload\frontend\build\windows\x64\plugins\system_tray\system_tray_plugin.dir\Release\app_window.obj
E:\github\local_upload\frontend\windows\flutter\ephemeral\.plugin_symlinks\system_tray\windows\menu_manager.cpp;E:\github\local_upload\frontend\build\windows\x64\plugins\system_tray\system_tray_plugin.dir\Release\menu_manager.obj
E:\github\local_upload\frontend\windows\flutter\ephemeral\.plugin_symlinks\system_tray\windows\menu.cpp;E:\github\local_upload\frontend\build\windows\x64\plugins\system_tray\system_tray_plugin.dir\Release\menu.obj
E:\github\local_upload\frontend\windows\flutter\ephemeral\.plugin_symlinks\system_tray\windows\tray.cpp;E:\github\local_upload\frontend\build\windows\x64\plugins\system_tray\system_tray_plugin.dir\Release\tray.obj
