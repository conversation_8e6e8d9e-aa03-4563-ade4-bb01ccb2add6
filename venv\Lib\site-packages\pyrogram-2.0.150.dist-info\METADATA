Metadata-Version: 2.4
Name: pyrogram
Version: 2.0.150
Summary: Elegant, modern and asynchronous Telegram MTProto API framework in Python for users and bots
Project-URL: Homepage, https://kurigram.live
Project-URL: Documentation, https://docs.kurigram.live
Project-URL: Source, https://github.com/KurimuzonAkuma/pyrogram
Project-URL: Issues, https://github.com/KurimuzonAkuma/pyrogram/issues
Project-URL: Community, https://t.me/kurigram_chat
Author-email: Dan <<EMAIL>>
Maintainer: KurimuzonAkuma
License-Expression: LGPL-3.0-or-later
License-File: COPYING
License-File: COPYING.lesser
Keywords: api,chat,client,library,messenger,mtproto,python,telegram
Classifier: Development Status :: 5 - Production/Stable
Classifier: Intended Audience :: Developers
Classifier: License :: OSI Approved :: GNU Lesser General Public License v3 (LGPLv3)
Classifier: Natural Language :: English
Classifier: Operating System :: OS Independent
Classifier: Programming Language :: Python
Classifier: Programming Language :: Python :: 3
Classifier: Programming Language :: Python :: 3.8
Classifier: Programming Language :: Python :: 3.9
Classifier: Programming Language :: Python :: 3.10
Classifier: Programming Language :: Python :: 3.11
Classifier: Programming Language :: Python :: 3.12
Classifier: Programming Language :: Python :: 3.13
Classifier: Programming Language :: Python :: Implementation
Classifier: Programming Language :: Python :: Implementation :: CPython
Classifier: Programming Language :: Python :: Implementation :: PyPy
Classifier: Topic :: Communications
Classifier: Topic :: Communications :: Chat
Classifier: Topic :: Internet
Classifier: Topic :: Software Development :: Libraries
Classifier: Topic :: Software Development :: Libraries :: Application Frameworks
Classifier: Topic :: Software Development :: Libraries :: Python Modules
Requires-Python: >=3.8
Requires-Dist: pyaes<=1.6.1
Requires-Dist: pysocks<=1.7.1
Provides-Extra: dev
Requires-Dist: hatch<=1.7.0; extra == 'dev'
Requires-Dist: keyring<=25.1.0; extra == 'dev'
Requires-Dist: pytest-asyncio<=0.21.1; extra == 'dev'
Requires-Dist: pytest-cov<=4.1.0; extra == 'dev'
Requires-Dist: pytest<=7.4.3; extra == 'dev'
Requires-Dist: twine<=4.0.2; extra == 'dev'
Provides-Extra: docs
Requires-Dist: furo<=2025.7.19; extra == 'docs'
Requires-Dist: pygments<=2.19.2; extra == 'docs'
Requires-Dist: sphinx-autobuild<=2024.10.3; extra == 'docs'
Requires-Dist: sphinx-copybutton<=0.5.2; extra == 'docs'
Requires-Dist: sphinx<=8.2.3; extra == 'docs'
Provides-Extra: fast
Requires-Dist: tgcrypto<=1.2.5; extra == 'fast'
Requires-Dist: uvloop<=0.21.0; (sys_platform == 'darwin' or sys_platform == 'linux') and extra == 'fast'
Description-Content-Type: text/markdown

<p align="center">
    <a href="https://github.com/KurimuzonAkuma/pyrogram">
        <img src="https://raw.githubusercontent.com/KurimuzonAkuma/kurigramartwork/master/kurigram-logo.png" alt="Pyrogram" width="128">
    </a>
    <br>
    <b>Telegram MTProto API Framework for Python</b>
    <br>
    <a href="https://kurigram.live">
        Homepage
    </a>
    •
    <a href="https://docs.kurigram.live">
        Documentation
    </a>
    •
    <a href="https://t.me/kurigram_news">
        News
    </a>
    •
    <a href="https://t.me/kurigram_chat">
        Chat
    </a>
</p>

## Pyrogram

> [!NOTE]
> Unfortunately, the original pyrogram is no longer supported. I will try to be your @delivrance.

> Elegant, modern and asynchronous Telegram MTProto API framework in Python for users and bots

``` python
from pyrogram import Client, filters

app = Client("my_account")


@app.on_message(filters.private)
async def hello(client, message):
    await message.reply("Hello from Pyrogram!")


app.run()
```

**Pyrogram** is a modern, elegant and asynchronous [MTProto API](https://docs.kurigram.live/topics/mtproto-vs-botapi)
framework. It enables you to easily interact with the main Telegram API through a user account (custom client) or a bot
identity (bot API alternative) using Python.

### Support

If you'd like to support my fork, you can consider:

- `kurimuzonakuma.ton` - TON
- `TCbZ7CSpTvTJ6rno2eoWWYBx7hmYF75wk3` - USDT TRC20

### Key Features

- **Ready**: Install Pyrogram with pip and start building your applications right away.
- **Easy**: Makes the Telegram API simple and intuitive, while still allowing advanced usages.
- **Elegant**: Low-level details are abstracted and re-presented in a more convenient way.
- **Fast**: Boosted up by [TgCrypto](https://github.com/pyrogram/tgcrypto), a high-performance cryptography library written in C.
- **Type-hinted**: Types and methods are all type-hinted, enabling excellent editor support.
- **Async**: Fully asynchronous (also usable synchronously if wanted, for convenience).
- **Powerful**: Full access to Telegram's API to execute any official client action and more.

### Installing

Stable version

``` bash
pip3 install kurigram
```

Dev version
``` bash
pip3 install https://github.com/KurimuzonAkuma/pyrogram/archive/dev.zip --force-reinstall
```

### Resources

- Check out the [docs](https://docs.kurigram.live) to learn more about Pyrogram, get started right
away and discover more in-depth material for building your client applications.
- Join the [official channel](https://t.me/kurigram_news) and stay tuned for news, updates and announcements.
- Join the [official chat](https://t.me/kurigram_chat) to communicate with people.
