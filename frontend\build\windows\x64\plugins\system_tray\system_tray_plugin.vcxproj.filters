﻿<?xml version="1.0" encoding="utf-8"?>
<Project ToolsVersion="17.0" xmlns="http://schemas.microsoft.com/developer/msbuild/2003">
  <ItemGroup>
    <ClCompile Include="E:\github\local_upload\frontend\windows\flutter\ephemeral\.plugin_symlinks\system_tray\windows\system_tray_plugin.cpp">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="E:\github\local_upload\frontend\windows\flutter\ephemeral\.plugin_symlinks\system_tray\windows\utils.cpp">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="E:\github\local_upload\frontend\windows\flutter\ephemeral\.plugin_symlinks\system_tray\windows\errors.cpp">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="E:\github\local_upload\frontend\windows\flutter\ephemeral\.plugin_symlinks\system_tray\windows\app_window.cpp">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="E:\github\local_upload\frontend\windows\flutter\ephemeral\.plugin_symlinks\system_tray\windows\menu_manager.cpp">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="E:\github\local_upload\frontend\windows\flutter\ephemeral\.plugin_symlinks\system_tray\windows\menu.cpp">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="E:\github\local_upload\frontend\windows\flutter\ephemeral\.plugin_symlinks\system_tray\windows\tray.cpp">
      <Filter>Source Files</Filter>
    </ClCompile>
  </ItemGroup>
  <ItemGroup>
    <CustomBuild Include="E:\github\local_upload\frontend\windows\flutter\ephemeral\.plugin_symlinks\system_tray\windows\CMakeLists.txt" />
  </ItemGroup>
  <ItemGroup>
    <Filter Include="Source Files">
      <UniqueIdentifier>{096B0447-651C-39D1-803D-35B402FCF074}</UniqueIdentifier>
    </Filter>
  </ItemGroup>
</Project>
