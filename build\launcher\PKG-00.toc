('E:\\github\\local_upload\\build\\launcher\\TelegramUploadTool.pkg',
 {'BINARY': True,
  'DATA': True,
  'EXECUTABLE': True,
  'EXTENSION': True,
  'PYMODULE': True,
  'PYSOURCE': True,
  'PYZ': False,
  'SPLASH': True,
  'SYMLINK': False},
 [('pyi-contents-directory _internal', '', 'OPTION'),
  ('PYZ-00.pyz',
   'E:\\github\\local_upload\\build\\launcher\\PYZ-00.pyz',
   'PYZ'),
  ('struct',
   'E:\\github\\local_upload\\build\\launcher\\localpycs\\struct.pyc',
   'PYMODULE'),
  ('pyimod01_archive',
   'E:\\github\\local_upload\\build\\launcher\\localpycs\\pyimod01_archive.pyc',
   'PYMODULE'),
  ('pyimod02_importers',
   'E:\\github\\local_upload\\build\\launcher\\localpycs\\pyimod02_importers.pyc',
   'PYMODULE'),
  ('pyimod03_ctypes',
   'E:\\github\\local_upload\\build\\launcher\\localpycs\\pyimod03_ctypes.pyc',
   'PYMODULE'),
  ('pyimod04_pywin32',
   'E:\\github\\local_upload\\build\\launcher\\localpycs\\pyimod04_pywin32.pyc',
   'PYMODULE'),
  ('pyiboot01_bootstrap',
   'E:\\github\\local_upload\\venv\\Lib\\site-packages\\PyInstaller\\loader\\pyiboot01_bootstrap.py',
   'PYSOURCE'),
  ('pyi_rth_inspect',
   'E:\\github\\local_upload\\venv\\Lib\\site-packages\\PyInstaller\\hooks\\rthooks\\pyi_rth_inspect.py',
   'PYSOURCE'),
  ('launcher', 'E:\\github\\local_upload\\launcher.py', 'PYSOURCE'),
  ('backend\\dist\\telegram_backend\\_internal\\VCRUNTIME140.dll',
   'E:\\github\\local_upload\\backend\\dist\\telegram_backend\\_internal\\VCRUNTIME140.dll',
   'BINARY'),
  ('backend\\dist\\telegram_backend\\_internal\\VCRUNTIME140_1.dll',
   'E:\\github\\local_upload\\backend\\dist\\telegram_backend\\_internal\\VCRUNTIME140_1.dll',
   'BINARY'),
  ('backend\\dist\\telegram_backend\\_internal\\_asyncio.pyd',
   'E:\\github\\local_upload\\backend\\dist\\telegram_backend\\_internal\\_asyncio.pyd',
   'BINARY'),
  ('backend\\dist\\telegram_backend\\_internal\\_bz2.pyd',
   'E:\\github\\local_upload\\backend\\dist\\telegram_backend\\_internal\\_bz2.pyd',
   'BINARY'),
  ('backend\\dist\\telegram_backend\\_internal\\_ctypes.pyd',
   'E:\\github\\local_upload\\backend\\dist\\telegram_backend\\_internal\\_ctypes.pyd',
   'BINARY'),
  ('backend\\dist\\telegram_backend\\_internal\\_decimal.pyd',
   'E:\\github\\local_upload\\backend\\dist\\telegram_backend\\_internal\\_decimal.pyd',
   'BINARY'),
  ('backend\\dist\\telegram_backend\\_internal\\_hashlib.pyd',
   'E:\\github\\local_upload\\backend\\dist\\telegram_backend\\_internal\\_hashlib.pyd',
   'BINARY'),
  ('backend\\dist\\telegram_backend\\_internal\\_lzma.pyd',
   'E:\\github\\local_upload\\backend\\dist\\telegram_backend\\_internal\\_lzma.pyd',
   'BINARY'),
  ('backend\\dist\\telegram_backend\\_internal\\_multiprocessing.pyd',
   'E:\\github\\local_upload\\backend\\dist\\telegram_backend\\_internal\\_multiprocessing.pyd',
   'BINARY'),
  ('backend\\dist\\telegram_backend\\_internal\\_overlapped.pyd',
   'E:\\github\\local_upload\\backend\\dist\\telegram_backend\\_internal\\_overlapped.pyd',
   'BINARY'),
  ('backend\\dist\\telegram_backend\\_internal\\_queue.pyd',
   'E:\\github\\local_upload\\backend\\dist\\telegram_backend\\_internal\\_queue.pyd',
   'BINARY'),
  ('backend\\dist\\telegram_backend\\_internal\\_socket.pyd',
   'E:\\github\\local_upload\\backend\\dist\\telegram_backend\\_internal\\_socket.pyd',
   'BINARY'),
  ('backend\\dist\\telegram_backend\\_internal\\_sqlite3.pyd',
   'E:\\github\\local_upload\\backend\\dist\\telegram_backend\\_internal\\_sqlite3.pyd',
   'BINARY'),
  ('backend\\dist\\telegram_backend\\_internal\\_ssl.pyd',
   'E:\\github\\local_upload\\backend\\dist\\telegram_backend\\_internal\\_ssl.pyd',
   'BINARY'),
  ('backend\\dist\\telegram_backend\\_internal\\_uuid.pyd',
   'E:\\github\\local_upload\\backend\\dist\\telegram_backend\\_internal\\_uuid.pyd',
   'BINARY'),
  ('backend\\dist\\telegram_backend\\_internal\\_wmi.pyd',
   'E:\\github\\local_upload\\backend\\dist\\telegram_backend\\_internal\\_wmi.pyd',
   'BINARY'),
  ('backend\\dist\\telegram_backend\\_internal\\api-ms-win-core-console-l1-1-0.dll',
   'E:\\github\\local_upload\\backend\\dist\\telegram_backend\\_internal\\api-ms-win-core-console-l1-1-0.dll',
   'BINARY'),
  ('backend\\dist\\telegram_backend\\_internal\\api-ms-win-core-datetime-l1-1-0.dll',
   'E:\\github\\local_upload\\backend\\dist\\telegram_backend\\_internal\\api-ms-win-core-datetime-l1-1-0.dll',
   'BINARY'),
  ('backend\\dist\\telegram_backend\\_internal\\api-ms-win-core-debug-l1-1-0.dll',
   'E:\\github\\local_upload\\backend\\dist\\telegram_backend\\_internal\\api-ms-win-core-debug-l1-1-0.dll',
   'BINARY'),
  ('backend\\dist\\telegram_backend\\_internal\\api-ms-win-core-errorhandling-l1-1-0.dll',
   'E:\\github\\local_upload\\backend\\dist\\telegram_backend\\_internal\\api-ms-win-core-errorhandling-l1-1-0.dll',
   'BINARY'),
  ('backend\\dist\\telegram_backend\\_internal\\api-ms-win-core-fibers-l1-1-0.dll',
   'E:\\github\\local_upload\\backend\\dist\\telegram_backend\\_internal\\api-ms-win-core-fibers-l1-1-0.dll',
   'BINARY'),
  ('backend\\dist\\telegram_backend\\_internal\\api-ms-win-core-file-l1-1-0.dll',
   'E:\\github\\local_upload\\backend\\dist\\telegram_backend\\_internal\\api-ms-win-core-file-l1-1-0.dll',
   'BINARY'),
  ('backend\\dist\\telegram_backend\\_internal\\api-ms-win-core-file-l1-2-0.dll',
   'E:\\github\\local_upload\\backend\\dist\\telegram_backend\\_internal\\api-ms-win-core-file-l1-2-0.dll',
   'BINARY'),
  ('backend\\dist\\telegram_backend\\_internal\\api-ms-win-core-file-l2-1-0.dll',
   'E:\\github\\local_upload\\backend\\dist\\telegram_backend\\_internal\\api-ms-win-core-file-l2-1-0.dll',
   'BINARY'),
  ('backend\\dist\\telegram_backend\\_internal\\api-ms-win-core-handle-l1-1-0.dll',
   'E:\\github\\local_upload\\backend\\dist\\telegram_backend\\_internal\\api-ms-win-core-handle-l1-1-0.dll',
   'BINARY'),
  ('backend\\dist\\telegram_backend\\_internal\\api-ms-win-core-heap-l1-1-0.dll',
   'E:\\github\\local_upload\\backend\\dist\\telegram_backend\\_internal\\api-ms-win-core-heap-l1-1-0.dll',
   'BINARY'),
  ('backend\\dist\\telegram_backend\\_internal\\api-ms-win-core-interlocked-l1-1-0.dll',
   'E:\\github\\local_upload\\backend\\dist\\telegram_backend\\_internal\\api-ms-win-core-interlocked-l1-1-0.dll',
   'BINARY'),
  ('backend\\dist\\telegram_backend\\_internal\\api-ms-win-core-libraryloader-l1-1-0.dll',
   'E:\\github\\local_upload\\backend\\dist\\telegram_backend\\_internal\\api-ms-win-core-libraryloader-l1-1-0.dll',
   'BINARY'),
  ('backend\\dist\\telegram_backend\\_internal\\api-ms-win-core-localization-l1-2-0.dll',
   'E:\\github\\local_upload\\backend\\dist\\telegram_backend\\_internal\\api-ms-win-core-localization-l1-2-0.dll',
   'BINARY'),
  ('backend\\dist\\telegram_backend\\_internal\\api-ms-win-core-memory-l1-1-0.dll',
   'E:\\github\\local_upload\\backend\\dist\\telegram_backend\\_internal\\api-ms-win-core-memory-l1-1-0.dll',
   'BINARY'),
  ('backend\\dist\\telegram_backend\\_internal\\api-ms-win-core-namedpipe-l1-1-0.dll',
   'E:\\github\\local_upload\\backend\\dist\\telegram_backend\\_internal\\api-ms-win-core-namedpipe-l1-1-0.dll',
   'BINARY'),
  ('backend\\dist\\telegram_backend\\_internal\\api-ms-win-core-processenvironment-l1-1-0.dll',
   'E:\\github\\local_upload\\backend\\dist\\telegram_backend\\_internal\\api-ms-win-core-processenvironment-l1-1-0.dll',
   'BINARY'),
  ('backend\\dist\\telegram_backend\\_internal\\api-ms-win-core-processthreads-l1-1-0.dll',
   'E:\\github\\local_upload\\backend\\dist\\telegram_backend\\_internal\\api-ms-win-core-processthreads-l1-1-0.dll',
   'BINARY'),
  ('backend\\dist\\telegram_backend\\_internal\\api-ms-win-core-processthreads-l1-1-1.dll',
   'E:\\github\\local_upload\\backend\\dist\\telegram_backend\\_internal\\api-ms-win-core-processthreads-l1-1-1.dll',
   'BINARY'),
  ('backend\\dist\\telegram_backend\\_internal\\api-ms-win-core-profile-l1-1-0.dll',
   'E:\\github\\local_upload\\backend\\dist\\telegram_backend\\_internal\\api-ms-win-core-profile-l1-1-0.dll',
   'BINARY'),
  ('backend\\dist\\telegram_backend\\_internal\\api-ms-win-core-rtlsupport-l1-1-0.dll',
   'E:\\github\\local_upload\\backend\\dist\\telegram_backend\\_internal\\api-ms-win-core-rtlsupport-l1-1-0.dll',
   'BINARY'),
  ('backend\\dist\\telegram_backend\\_internal\\api-ms-win-core-string-l1-1-0.dll',
   'E:\\github\\local_upload\\backend\\dist\\telegram_backend\\_internal\\api-ms-win-core-string-l1-1-0.dll',
   'BINARY'),
  ('backend\\dist\\telegram_backend\\_internal\\api-ms-win-core-synch-l1-1-0.dll',
   'E:\\github\\local_upload\\backend\\dist\\telegram_backend\\_internal\\api-ms-win-core-synch-l1-1-0.dll',
   'BINARY'),
  ('backend\\dist\\telegram_backend\\_internal\\api-ms-win-core-synch-l1-2-0.dll',
   'E:\\github\\local_upload\\backend\\dist\\telegram_backend\\_internal\\api-ms-win-core-synch-l1-2-0.dll',
   'BINARY'),
  ('backend\\dist\\telegram_backend\\_internal\\api-ms-win-core-sysinfo-l1-1-0.dll',
   'E:\\github\\local_upload\\backend\\dist\\telegram_backend\\_internal\\api-ms-win-core-sysinfo-l1-1-0.dll',
   'BINARY'),
  ('backend\\dist\\telegram_backend\\_internal\\api-ms-win-core-timezone-l1-1-0.dll',
   'E:\\github\\local_upload\\backend\\dist\\telegram_backend\\_internal\\api-ms-win-core-timezone-l1-1-0.dll',
   'BINARY'),
  ('backend\\dist\\telegram_backend\\_internal\\api-ms-win-core-util-l1-1-0.dll',
   'E:\\github\\local_upload\\backend\\dist\\telegram_backend\\_internal\\api-ms-win-core-util-l1-1-0.dll',
   'BINARY'),
  ('backend\\dist\\telegram_backend\\_internal\\api-ms-win-crt-conio-l1-1-0.dll',
   'E:\\github\\local_upload\\backend\\dist\\telegram_backend\\_internal\\api-ms-win-crt-conio-l1-1-0.dll',
   'BINARY'),
  ('backend\\dist\\telegram_backend\\_internal\\api-ms-win-crt-convert-l1-1-0.dll',
   'E:\\github\\local_upload\\backend\\dist\\telegram_backend\\_internal\\api-ms-win-crt-convert-l1-1-0.dll',
   'BINARY'),
  ('backend\\dist\\telegram_backend\\_internal\\api-ms-win-crt-environment-l1-1-0.dll',
   'E:\\github\\local_upload\\backend\\dist\\telegram_backend\\_internal\\api-ms-win-crt-environment-l1-1-0.dll',
   'BINARY'),
  ('backend\\dist\\telegram_backend\\_internal\\api-ms-win-crt-filesystem-l1-1-0.dll',
   'E:\\github\\local_upload\\backend\\dist\\telegram_backend\\_internal\\api-ms-win-crt-filesystem-l1-1-0.dll',
   'BINARY'),
  ('backend\\dist\\telegram_backend\\_internal\\api-ms-win-crt-heap-l1-1-0.dll',
   'E:\\github\\local_upload\\backend\\dist\\telegram_backend\\_internal\\api-ms-win-crt-heap-l1-1-0.dll',
   'BINARY'),
  ('backend\\dist\\telegram_backend\\_internal\\api-ms-win-crt-locale-l1-1-0.dll',
   'E:\\github\\local_upload\\backend\\dist\\telegram_backend\\_internal\\api-ms-win-crt-locale-l1-1-0.dll',
   'BINARY'),
  ('backend\\dist\\telegram_backend\\_internal\\api-ms-win-crt-math-l1-1-0.dll',
   'E:\\github\\local_upload\\backend\\dist\\telegram_backend\\_internal\\api-ms-win-crt-math-l1-1-0.dll',
   'BINARY'),
  ('backend\\dist\\telegram_backend\\_internal\\api-ms-win-crt-process-l1-1-0.dll',
   'E:\\github\\local_upload\\backend\\dist\\telegram_backend\\_internal\\api-ms-win-crt-process-l1-1-0.dll',
   'BINARY'),
  ('backend\\dist\\telegram_backend\\_internal\\api-ms-win-crt-runtime-l1-1-0.dll',
   'E:\\github\\local_upload\\backend\\dist\\telegram_backend\\_internal\\api-ms-win-crt-runtime-l1-1-0.dll',
   'BINARY'),
  ('backend\\dist\\telegram_backend\\_internal\\api-ms-win-crt-stdio-l1-1-0.dll',
   'E:\\github\\local_upload\\backend\\dist\\telegram_backend\\_internal\\api-ms-win-crt-stdio-l1-1-0.dll',
   'BINARY'),
  ('backend\\dist\\telegram_backend\\_internal\\api-ms-win-crt-string-l1-1-0.dll',
   'E:\\github\\local_upload\\backend\\dist\\telegram_backend\\_internal\\api-ms-win-crt-string-l1-1-0.dll',
   'BINARY'),
  ('backend\\dist\\telegram_backend\\_internal\\api-ms-win-crt-time-l1-1-0.dll',
   'E:\\github\\local_upload\\backend\\dist\\telegram_backend\\_internal\\api-ms-win-crt-time-l1-1-0.dll',
   'BINARY'),
  ('backend\\dist\\telegram_backend\\_internal\\api-ms-win-crt-utility-l1-1-0.dll',
   'E:\\github\\local_upload\\backend\\dist\\telegram_backend\\_internal\\api-ms-win-crt-utility-l1-1-0.dll',
   'BINARY'),
  ('backend\\dist\\telegram_backend\\_internal\\libcrypto-3.dll',
   'E:\\github\\local_upload\\backend\\dist\\telegram_backend\\_internal\\libcrypto-3.dll',
   'BINARY'),
  ('backend\\dist\\telegram_backend\\_internal\\libffi-8.dll',
   'E:\\github\\local_upload\\backend\\dist\\telegram_backend\\_internal\\libffi-8.dll',
   'BINARY'),
  ('backend\\dist\\telegram_backend\\_internal\\libssl-3.dll',
   'E:\\github\\local_upload\\backend\\dist\\telegram_backend\\_internal\\libssl-3.dll',
   'BINARY'),
  ('backend\\dist\\telegram_backend\\_internal\\markupsafe\\_speedups.cp312-win_amd64.pyd',
   'E:\\github\\local_upload\\backend\\dist\\telegram_backend\\_internal\\markupsafe\\_speedups.cp312-win_amd64.pyd',
   'BINARY'),
  ('backend\\dist\\telegram_backend\\_internal\\pyexpat.pyd',
   'E:\\github\\local_upload\\backend\\dist\\telegram_backend\\_internal\\pyexpat.pyd',
   'BINARY'),
  ('backend\\dist\\telegram_backend\\_internal\\python312.dll',
   'E:\\github\\local_upload\\backend\\dist\\telegram_backend\\_internal\\python312.dll',
   'BINARY'),
  ('backend\\dist\\telegram_backend\\_internal\\select.pyd',
   'E:\\github\\local_upload\\backend\\dist\\telegram_backend\\_internal\\select.pyd',
   'BINARY'),
  ('backend\\dist\\telegram_backend\\_internal\\sqlite3.dll',
   'E:\\github\\local_upload\\backend\\dist\\telegram_backend\\_internal\\sqlite3.dll',
   'BINARY'),
  ('backend\\dist\\telegram_backend\\_internal\\tgcrypto.cp312-win_amd64.pyd',
   'E:\\github\\local_upload\\backend\\dist\\telegram_backend\\_internal\\tgcrypto.cp312-win_amd64.pyd',
   'BINARY'),
  ('backend\\dist\\telegram_backend\\_internal\\ucrtbase.dll',
   'E:\\github\\local_upload\\backend\\dist\\telegram_backend\\_internal\\ucrtbase.dll',
   'BINARY'),
  ('backend\\dist\\telegram_backend\\_internal\\unicodedata.pyd',
   'E:\\github\\local_upload\\backend\\dist\\telegram_backend\\_internal\\unicodedata.pyd',
   'BINARY'),
  ('backend\\dist\\telegram_backend\\telegram_backend.exe',
   'E:\\github\\local_upload\\backend\\dist\\telegram_backend\\telegram_backend.exe',
   'BINARY'),
  ('frontend\\build\\windows\\x64\\runner\\Release\\flutter_windows.dll',
   'E:\\github\\local_upload\\frontend\\build\\windows\\x64\\runner\\Release\\flutter_windows.dll',
   'BINARY'),
  ('frontend\\build\\windows\\x64\\runner\\Release\\screen_retriever_plugin.dll',
   'E:\\github\\local_upload\\frontend\\build\\windows\\x64\\runner\\Release\\screen_retriever_plugin.dll',
   'BINARY'),
  ('frontend\\build\\windows\\x64\\runner\\Release\\system_tray_plugin.dll',
   'E:\\github\\local_upload\\frontend\\build\\windows\\x64\\runner\\Release\\system_tray_plugin.dll',
   'BINARY'),
  ('frontend\\build\\windows\\x64\\runner\\Release\\telegram_uploader.exe',
   'E:\\github\\local_upload\\frontend\\build\\windows\\x64\\runner\\Release\\telegram_uploader.exe',
   'BINARY'),
  ('frontend\\build\\windows\\x64\\runner\\Release\\window_manager_plugin.dll',
   'E:\\github\\local_upload\\frontend\\build\\windows\\x64\\runner\\Release\\window_manager_plugin.dll',
   'BINARY'),
  ('python312.dll', 'C:\\Python312\\python312.dll', 'BINARY'),
  ('_decimal.pyd', 'C:\\Python312\\DLLs\\_decimal.pyd', 'EXTENSION'),
  ('_hashlib.pyd', 'C:\\Python312\\DLLs\\_hashlib.pyd', 'EXTENSION'),
  ('_lzma.pyd', 'C:\\Python312\\DLLs\\_lzma.pyd', 'EXTENSION'),
  ('_bz2.pyd', 'C:\\Python312\\DLLs\\_bz2.pyd', 'EXTENSION'),
  ('unicodedata.pyd', 'C:\\Python312\\DLLs\\unicodedata.pyd', 'EXTENSION'),
  ('select.pyd', 'C:\\Python312\\DLLs\\select.pyd', 'EXTENSION'),
  ('_socket.pyd', 'C:\\Python312\\DLLs\\_socket.pyd', 'EXTENSION'),
  ('_queue.pyd', 'C:\\Python312\\DLLs\\_queue.pyd', 'EXTENSION'),
  ('_ssl.pyd', 'C:\\Python312\\DLLs\\_ssl.pyd', 'EXTENSION'),
  ('charset_normalizer\\md__mypyc.cp312-win_amd64.pyd',
   'E:\\github\\local_upload\\venv\\Lib\\site-packages\\charset_normalizer\\md__mypyc.cp312-win_amd64.pyd',
   'EXTENSION'),
  ('charset_normalizer\\md.cp312-win_amd64.pyd',
   'E:\\github\\local_upload\\venv\\Lib\\site-packages\\charset_normalizer\\md.cp312-win_amd64.pyd',
   'EXTENSION'),
  ('api-ms-win-crt-string-l1-1-0.dll',
   'E:\\github\\local_upload\\backend\\dist\\telegram_backend\\_internal\\api-ms-win-crt-string-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-crt-stdio-l1-1-0.dll',
   'E:\\github\\local_upload\\backend\\dist\\telegram_backend\\_internal\\api-ms-win-crt-stdio-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-crt-heap-l1-1-0.dll',
   'E:\\github\\local_upload\\backend\\dist\\telegram_backend\\_internal\\api-ms-win-crt-heap-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-crt-convert-l1-1-0.dll',
   'E:\\github\\local_upload\\backend\\dist\\telegram_backend\\_internal\\api-ms-win-crt-convert-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-crt-runtime-l1-1-0.dll',
   'E:\\github\\local_upload\\backend\\dist\\telegram_backend\\_internal\\api-ms-win-crt-runtime-l1-1-0.dll',
   'BINARY'),
  ('VCRUNTIME140.dll',
   'E:\\github\\local_upload\\backend\\dist\\telegram_backend\\_internal\\VCRUNTIME140.dll',
   'BINARY'),
  ('libffi-8.dll',
   'E:\\github\\local_upload\\backend\\dist\\telegram_backend\\_internal\\libffi-8.dll',
   'BINARY'),
  ('api-ms-win-crt-locale-l1-1-0.dll',
   'E:\\github\\local_upload\\backend\\dist\\telegram_backend\\_internal\\api-ms-win-crt-locale-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-crt-math-l1-1-0.dll',
   'E:\\github\\local_upload\\backend\\dist\\telegram_backend\\_internal\\api-ms-win-crt-math-l1-1-0.dll',
   'BINARY'),
  ('libcrypto-3.dll',
   'E:\\github\\local_upload\\backend\\dist\\telegram_backend\\_internal\\libcrypto-3.dll',
   'BINARY'),
  ('sqlite3.dll',
   'E:\\github\\local_upload\\backend\\dist\\telegram_backend\\_internal\\sqlite3.dll',
   'BINARY'),
  ('libssl-3.dll',
   'E:\\github\\local_upload\\backend\\dist\\telegram_backend\\_internal\\libssl-3.dll',
   'BINARY'),
  ('VCRUNTIME140_1.dll',
   'E:\\github\\local_upload\\backend\\dist\\telegram_backend\\_internal\\VCRUNTIME140_1.dll',
   'BINARY'),
  ('ucrtbase.dll',
   'E:\\github\\local_upload\\backend\\dist\\telegram_backend\\_internal\\ucrtbase.dll',
   'BINARY'),
  ('api-ms-win-crt-utility-l1-1-0.dll',
   'E:\\github\\local_upload\\backend\\dist\\telegram_backend\\_internal\\api-ms-win-crt-utility-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-crt-environment-l1-1-0.dll',
   'E:\\github\\local_upload\\backend\\dist\\telegram_backend\\_internal\\api-ms-win-crt-environment-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-crt-time-l1-1-0.dll',
   'E:\\github\\local_upload\\backend\\dist\\telegram_backend\\_internal\\api-ms-win-crt-time-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-crt-filesystem-l1-1-0.dll',
   'E:\\github\\local_upload\\backend\\dist\\telegram_backend\\_internal\\api-ms-win-crt-filesystem-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-crt-process-l1-1-0.dll',
   'E:\\github\\local_upload\\backend\\dist\\telegram_backend\\_internal\\api-ms-win-crt-process-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-crt-conio-l1-1-0.dll',
   'E:\\github\\local_upload\\backend\\dist\\telegram_backend\\_internal\\api-ms-win-crt-conio-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-core-synch-l1-2-0.dll',
   'E:\\github\\local_upload\\backend\\dist\\telegram_backend\\_internal\\api-ms-win-core-synch-l1-2-0.dll',
   'BINARY'),
  ('api-ms-win-core-processthreads-l1-1-0.dll',
   'E:\\github\\local_upload\\backend\\dist\\telegram_backend\\_internal\\api-ms-win-core-processthreads-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-core-fibers-l1-1-0.dll',
   'E:\\github\\local_upload\\backend\\dist\\telegram_backend\\_internal\\api-ms-win-core-fibers-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-core-console-l1-1-0.dll',
   'E:\\github\\local_upload\\backend\\dist\\telegram_backend\\_internal\\api-ms-win-core-console-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-core-debug-l1-1-0.dll',
   'E:\\github\\local_upload\\backend\\dist\\telegram_backend\\_internal\\api-ms-win-core-debug-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-core-processenvironment-l1-1-0.dll',
   'E:\\github\\local_upload\\backend\\dist\\telegram_backend\\_internal\\api-ms-win-core-processenvironment-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-core-string-l1-1-0.dll',
   'E:\\github\\local_upload\\backend\\dist\\telegram_backend\\_internal\\api-ms-win-core-string-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-core-synch-l1-1-0.dll',
   'E:\\github\\local_upload\\backend\\dist\\telegram_backend\\_internal\\api-ms-win-core-synch-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-core-file-l1-1-0.dll',
   'E:\\github\\local_upload\\backend\\dist\\telegram_backend\\_internal\\api-ms-win-core-file-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-core-libraryloader-l1-1-0.dll',
   'E:\\github\\local_upload\\backend\\dist\\telegram_backend\\_internal\\api-ms-win-core-libraryloader-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-core-heap-l1-1-0.dll',
   'E:\\github\\local_upload\\backend\\dist\\telegram_backend\\_internal\\api-ms-win-core-heap-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-core-namedpipe-l1-1-0.dll',
   'E:\\github\\local_upload\\backend\\dist\\telegram_backend\\_internal\\api-ms-win-core-namedpipe-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-core-datetime-l1-1-0.dll',
   'E:\\github\\local_upload\\backend\\dist\\telegram_backend\\_internal\\api-ms-win-core-datetime-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-core-profile-l1-1-0.dll',
   'E:\\github\\local_upload\\backend\\dist\\telegram_backend\\_internal\\api-ms-win-core-profile-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-core-timezone-l1-1-0.dll',
   'E:\\github\\local_upload\\backend\\dist\\telegram_backend\\_internal\\api-ms-win-core-timezone-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-core-handle-l1-1-0.dll',
   'E:\\github\\local_upload\\backend\\dist\\telegram_backend\\_internal\\api-ms-win-core-handle-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-core-memory-l1-1-0.dll',
   'E:\\github\\local_upload\\backend\\dist\\telegram_backend\\_internal\\api-ms-win-core-memory-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-core-util-l1-1-0.dll',
   'E:\\github\\local_upload\\backend\\dist\\telegram_backend\\_internal\\api-ms-win-core-util-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-core-file-l1-2-0.dll',
   'E:\\github\\local_upload\\backend\\dist\\telegram_backend\\_internal\\api-ms-win-core-file-l1-2-0.dll',
   'BINARY'),
  ('api-ms-win-core-file-l2-1-0.dll',
   'E:\\github\\local_upload\\backend\\dist\\telegram_backend\\_internal\\api-ms-win-core-file-l2-1-0.dll',
   'BINARY'),
  ('api-ms-win-core-sysinfo-l1-1-0.dll',
   'E:\\github\\local_upload\\backend\\dist\\telegram_backend\\_internal\\api-ms-win-core-sysinfo-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-core-errorhandling-l1-1-0.dll',
   'E:\\github\\local_upload\\backend\\dist\\telegram_backend\\_internal\\api-ms-win-core-errorhandling-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-core-interlocked-l1-1-0.dll',
   'E:\\github\\local_upload\\backend\\dist\\telegram_backend\\_internal\\api-ms-win-core-interlocked-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-core-localization-l1-2-0.dll',
   'E:\\github\\local_upload\\backend\\dist\\telegram_backend\\_internal\\api-ms-win-core-localization-l1-2-0.dll',
   'BINARY'),
  ('api-ms-win-core-rtlsupport-l1-1-0.dll',
   'E:\\github\\local_upload\\backend\\dist\\telegram_backend\\_internal\\api-ms-win-core-rtlsupport-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-core-processthreads-l1-1-1.dll',
   'E:\\github\\local_upload\\backend\\dist\\telegram_backend\\_internal\\api-ms-win-core-processthreads-l1-1-1.dll',
   'BINARY'),
  ('flutter_windows.dll',
   'E:\\github\\local_upload\\frontend\\build\\windows\\x64\\runner\\Release\\flutter_windows.dll',
   'BINARY'),
  ('MSVCP140.dll', 'C:\\WINDOWS\\system32\\MSVCP140.dll', 'BINARY'),
  ('window_manager_plugin.dll',
   'E:\\github\\local_upload\\frontend\\build\\windows\\x64\\runner\\Release\\window_manager_plugin.dll',
   'BINARY'),
  ('screen_retriever_plugin.dll',
   'E:\\github\\local_upload\\frontend\\build\\windows\\x64\\runner\\Release\\screen_retriever_plugin.dll',
   'BINARY'),
  ('system_tray_plugin.dll',
   'E:\\github\\local_upload\\frontend\\build\\windows\\x64\\runner\\Release\\system_tray_plugin.dll',
   'BINARY'),
  ('backend\\dist\\telegram_backend\\_internal\\MarkupSafe-3.0.2.dist-info\\INSTALLER',
   'E:\\github\\local_upload\\backend\\dist\\telegram_backend\\_internal\\MarkupSafe-3.0.2.dist-info\\INSTALLER',
   'DATA'),
  ('backend\\dist\\telegram_backend\\_internal\\MarkupSafe-3.0.2.dist-info\\LICENSE.txt',
   'E:\\github\\local_upload\\backend\\dist\\telegram_backend\\_internal\\MarkupSafe-3.0.2.dist-info\\LICENSE.txt',
   'DATA'),
  ('backend\\dist\\telegram_backend\\_internal\\MarkupSafe-3.0.2.dist-info\\METADATA',
   'E:\\github\\local_upload\\backend\\dist\\telegram_backend\\_internal\\MarkupSafe-3.0.2.dist-info\\METADATA',
   'DATA'),
  ('backend\\dist\\telegram_backend\\_internal\\MarkupSafe-3.0.2.dist-info\\RECORD',
   'E:\\github\\local_upload\\backend\\dist\\telegram_backend\\_internal\\MarkupSafe-3.0.2.dist-info\\RECORD',
   'DATA'),
  ('backend\\dist\\telegram_backend\\_internal\\MarkupSafe-3.0.2.dist-info\\WHEEL',
   'E:\\github\\local_upload\\backend\\dist\\telegram_backend\\_internal\\MarkupSafe-3.0.2.dist-info\\WHEEL',
   'DATA'),
  ('backend\\dist\\telegram_backend\\_internal\\MarkupSafe-3.0.2.dist-info\\top_level.txt',
   'E:\\github\\local_upload\\backend\\dist\\telegram_backend\\_internal\\MarkupSafe-3.0.2.dist-info\\top_level.txt',
   'DATA'),
  ('backend\\dist\\telegram_backend\\_internal\\base_library.zip',
   'E:\\github\\local_upload\\backend\\dist\\telegram_backend\\_internal\\base_library.zip',
   'DATA'),
  ('backend\\dist\\telegram_backend\\_internal\\click-8.2.1.dist-info\\INSTALLER',
   'E:\\github\\local_upload\\backend\\dist\\telegram_backend\\_internal\\click-8.2.1.dist-info\\INSTALLER',
   'DATA'),
  ('backend\\dist\\telegram_backend\\_internal\\click-8.2.1.dist-info\\METADATA',
   'E:\\github\\local_upload\\backend\\dist\\telegram_backend\\_internal\\click-8.2.1.dist-info\\METADATA',
   'DATA'),
  ('backend\\dist\\telegram_backend\\_internal\\click-8.2.1.dist-info\\RECORD',
   'E:\\github\\local_upload\\backend\\dist\\telegram_backend\\_internal\\click-8.2.1.dist-info\\RECORD',
   'DATA'),
  ('backend\\dist\\telegram_backend\\_internal\\click-8.2.1.dist-info\\WHEEL',
   'E:\\github\\local_upload\\backend\\dist\\telegram_backend\\_internal\\click-8.2.1.dist-info\\WHEEL',
   'DATA'),
  ('backend\\dist\\telegram_backend\\_internal\\click-8.2.1.dist-info\\licenses\\LICENSE.txt',
   'E:\\github\\local_upload\\backend\\dist\\telegram_backend\\_internal\\click-8.2.1.dist-info\\licenses\\LICENSE.txt',
   'DATA'),
  ('backend\\dist\\telegram_backend\\_internal\\flask-2.3.3.dist-info\\INSTALLER',
   'E:\\github\\local_upload\\backend\\dist\\telegram_backend\\_internal\\flask-2.3.3.dist-info\\INSTALLER',
   'DATA'),
  ('backend\\dist\\telegram_backend\\_internal\\flask-2.3.3.dist-info\\LICENSE.rst',
   'E:\\github\\local_upload\\backend\\dist\\telegram_backend\\_internal\\flask-2.3.3.dist-info\\LICENSE.rst',
   'DATA'),
  ('backend\\dist\\telegram_backend\\_internal\\flask-2.3.3.dist-info\\METADATA',
   'E:\\github\\local_upload\\backend\\dist\\telegram_backend\\_internal\\flask-2.3.3.dist-info\\METADATA',
   'DATA'),
  ('backend\\dist\\telegram_backend\\_internal\\flask-2.3.3.dist-info\\RECORD',
   'E:\\github\\local_upload\\backend\\dist\\telegram_backend\\_internal\\flask-2.3.3.dist-info\\RECORD',
   'DATA'),
  ('backend\\dist\\telegram_backend\\_internal\\flask-2.3.3.dist-info\\REQUESTED',
   'E:\\github\\local_upload\\backend\\dist\\telegram_backend\\_internal\\flask-2.3.3.dist-info\\REQUESTED',
   'DATA'),
  ('backend\\dist\\telegram_backend\\_internal\\flask-2.3.3.dist-info\\WHEEL',
   'E:\\github\\local_upload\\backend\\dist\\telegram_backend\\_internal\\flask-2.3.3.dist-info\\WHEEL',
   'DATA'),
  ('backend\\dist\\telegram_backend\\_internal\\flask-2.3.3.dist-info\\entry_points.txt',
   'E:\\github\\local_upload\\backend\\dist\\telegram_backend\\_internal\\flask-2.3.3.dist-info\\entry_points.txt',
   'DATA'),
  ('backend\\dist\\telegram_backend\\_internal\\importlib_metadata-8.0.0.dist-info\\INSTALLER',
   'E:\\github\\local_upload\\backend\\dist\\telegram_backend\\_internal\\importlib_metadata-8.0.0.dist-info\\INSTALLER',
   'DATA'),
  ('backend\\dist\\telegram_backend\\_internal\\importlib_metadata-8.0.0.dist-info\\LICENSE',
   'E:\\github\\local_upload\\backend\\dist\\telegram_backend\\_internal\\importlib_metadata-8.0.0.dist-info\\LICENSE',
   'DATA'),
  ('backend\\dist\\telegram_backend\\_internal\\importlib_metadata-8.0.0.dist-info\\METADATA',
   'E:\\github\\local_upload\\backend\\dist\\telegram_backend\\_internal\\importlib_metadata-8.0.0.dist-info\\METADATA',
   'DATA'),
  ('backend\\dist\\telegram_backend\\_internal\\importlib_metadata-8.0.0.dist-info\\RECORD',
   'E:\\github\\local_upload\\backend\\dist\\telegram_backend\\_internal\\importlib_metadata-8.0.0.dist-info\\RECORD',
   'DATA'),
  ('backend\\dist\\telegram_backend\\_internal\\importlib_metadata-8.0.0.dist-info\\REQUESTED',
   'E:\\github\\local_upload\\backend\\dist\\telegram_backend\\_internal\\importlib_metadata-8.0.0.dist-info\\REQUESTED',
   'DATA'),
  ('backend\\dist\\telegram_backend\\_internal\\importlib_metadata-8.0.0.dist-info\\WHEEL',
   'E:\\github\\local_upload\\backend\\dist\\telegram_backend\\_internal\\importlib_metadata-8.0.0.dist-info\\WHEEL',
   'DATA'),
  ('backend\\dist\\telegram_backend\\_internal\\importlib_metadata-8.0.0.dist-info\\top_level.txt',
   'E:\\github\\local_upload\\backend\\dist\\telegram_backend\\_internal\\importlib_metadata-8.0.0.dist-info\\top_level.txt',
   'DATA'),
  ('backend\\dist\\telegram_backend\\_internal\\itsdangerous-2.2.0.dist-info\\INSTALLER',
   'E:\\github\\local_upload\\backend\\dist\\telegram_backend\\_internal\\itsdangerous-2.2.0.dist-info\\INSTALLER',
   'DATA'),
  ('backend\\dist\\telegram_backend\\_internal\\itsdangerous-2.2.0.dist-info\\LICENSE.txt',
   'E:\\github\\local_upload\\backend\\dist\\telegram_backend\\_internal\\itsdangerous-2.2.0.dist-info\\LICENSE.txt',
   'DATA'),
  ('backend\\dist\\telegram_backend\\_internal\\itsdangerous-2.2.0.dist-info\\METADATA',
   'E:\\github\\local_upload\\backend\\dist\\telegram_backend\\_internal\\itsdangerous-2.2.0.dist-info\\METADATA',
   'DATA'),
  ('backend\\dist\\telegram_backend\\_internal\\itsdangerous-2.2.0.dist-info\\RECORD',
   'E:\\github\\local_upload\\backend\\dist\\telegram_backend\\_internal\\itsdangerous-2.2.0.dist-info\\RECORD',
   'DATA'),
  ('backend\\dist\\telegram_backend\\_internal\\itsdangerous-2.2.0.dist-info\\WHEEL',
   'E:\\github\\local_upload\\backend\\dist\\telegram_backend\\_internal\\itsdangerous-2.2.0.dist-info\\WHEEL',
   'DATA'),
  ('backend\\dist\\telegram_backend\\_internal\\werkzeug-3.1.3.dist-info\\INSTALLER',
   'E:\\github\\local_upload\\backend\\dist\\telegram_backend\\_internal\\werkzeug-3.1.3.dist-info\\INSTALLER',
   'DATA'),
  ('backend\\dist\\telegram_backend\\_internal\\werkzeug-3.1.3.dist-info\\LICENSE.txt',
   'E:\\github\\local_upload\\backend\\dist\\telegram_backend\\_internal\\werkzeug-3.1.3.dist-info\\LICENSE.txt',
   'DATA'),
  ('backend\\dist\\telegram_backend\\_internal\\werkzeug-3.1.3.dist-info\\METADATA',
   'E:\\github\\local_upload\\backend\\dist\\telegram_backend\\_internal\\werkzeug-3.1.3.dist-info\\METADATA',
   'DATA'),
  ('backend\\dist\\telegram_backend\\_internal\\werkzeug-3.1.3.dist-info\\RECORD',
   'E:\\github\\local_upload\\backend\\dist\\telegram_backend\\_internal\\werkzeug-3.1.3.dist-info\\RECORD',
   'DATA'),
  ('backend\\dist\\telegram_backend\\_internal\\werkzeug-3.1.3.dist-info\\WHEEL',
   'E:\\github\\local_upload\\backend\\dist\\telegram_backend\\_internal\\werkzeug-3.1.3.dist-info\\WHEEL',
   'DATA'),
  ('frontend\\build\\windows\\x64\\runner\\Release\\data\\app.so',
   'E:\\github\\local_upload\\frontend\\build\\windows\\x64\\runner\\Release\\data\\app.so',
   'DATA'),
  ('frontend\\build\\windows\\x64\\runner\\Release\\data\\flutter_assets\\AssetManifest.bin',
   'E:\\github\\local_upload\\frontend\\build\\windows\\x64\\runner\\Release\\data\\flutter_assets\\AssetManifest.bin',
   'DATA'),
  ('frontend\\build\\windows\\x64\\runner\\Release\\data\\flutter_assets\\AssetManifest.json',
   'E:\\github\\local_upload\\frontend\\build\\windows\\x64\\runner\\Release\\data\\flutter_assets\\AssetManifest.json',
   'DATA'),
  ('frontend\\build\\windows\\x64\\runner\\Release\\data\\flutter_assets\\FontManifest.json',
   'E:\\github\\local_upload\\frontend\\build\\windows\\x64\\runner\\Release\\data\\flutter_assets\\FontManifest.json',
   'DATA'),
  ('frontend\\build\\windows\\x64\\runner\\Release\\data\\flutter_assets\\NOTICES.Z',
   'E:\\github\\local_upload\\frontend\\build\\windows\\x64\\runner\\Release\\data\\flutter_assets\\NOTICES.Z',
   'DATA'),
  ('frontend\\build\\windows\\x64\\runner\\Release\\data\\flutter_assets\\NativeAssetsManifest.json',
   'E:\\github\\local_upload\\frontend\\build\\windows\\x64\\runner\\Release\\data\\flutter_assets\\NativeAssetsManifest.json',
   'DATA'),
  ('frontend\\build\\windows\\x64\\runner\\Release\\data\\flutter_assets\\assets\\icons\\app_icon.ico',
   'E:\\github\\local_upload\\frontend\\build\\windows\\x64\\runner\\Release\\data\\flutter_assets\\assets\\icons\\app_icon.ico',
   'DATA'),
  ('frontend\\build\\windows\\x64\\runner\\Release\\data\\flutter_assets\\fonts\\MaterialIcons-Regular.otf',
   'E:\\github\\local_upload\\frontend\\build\\windows\\x64\\runner\\Release\\data\\flutter_assets\\fonts\\MaterialIcons-Regular.otf',
   'DATA'),
  ('frontend\\build\\windows\\x64\\runner\\Release\\data\\flutter_assets\\packages\\cupertino_icons\\assets\\CupertinoIcons.ttf',
   'E:\\github\\local_upload\\frontend\\build\\windows\\x64\\runner\\Release\\data\\flutter_assets\\packages\\cupertino_icons\\assets\\CupertinoIcons.ttf',
   'DATA'),
  ('frontend\\build\\windows\\x64\\runner\\Release\\data\\flutter_assets\\packages\\window_manager\\images\\ic_chrome_close.png',
   'E:\\github\\local_upload\\frontend\\build\\windows\\x64\\runner\\Release\\data\\flutter_assets\\packages\\window_manager\\images\\ic_chrome_close.png',
   'DATA'),
  ('frontend\\build\\windows\\x64\\runner\\Release\\data\\flutter_assets\\packages\\window_manager\\images\\ic_chrome_maximize.png',
   'E:\\github\\local_upload\\frontend\\build\\windows\\x64\\runner\\Release\\data\\flutter_assets\\packages\\window_manager\\images\\ic_chrome_maximize.png',
   'DATA'),
  ('frontend\\build\\windows\\x64\\runner\\Release\\data\\flutter_assets\\packages\\window_manager\\images\\ic_chrome_minimize.png',
   'E:\\github\\local_upload\\frontend\\build\\windows\\x64\\runner\\Release\\data\\flutter_assets\\packages\\window_manager\\images\\ic_chrome_minimize.png',
   'DATA'),
  ('frontend\\build\\windows\\x64\\runner\\Release\\data\\flutter_assets\\packages\\window_manager\\images\\ic_chrome_unmaximize.png',
   'E:\\github\\local_upload\\frontend\\build\\windows\\x64\\runner\\Release\\data\\flutter_assets\\packages\\window_manager\\images\\ic_chrome_unmaximize.png',
   'DATA'),
  ('frontend\\build\\windows\\x64\\runner\\Release\\data\\flutter_assets\\shaders\\ink_sparkle.frag',
   'E:\\github\\local_upload\\frontend\\build\\windows\\x64\\runner\\Release\\data\\flutter_assets\\shaders\\ink_sparkle.frag',
   'DATA'),
  ('frontend\\build\\windows\\x64\\runner\\Release\\data\\icudtl.dat',
   'E:\\github\\local_upload\\frontend\\build\\windows\\x64\\runner\\Release\\data\\icudtl.dat',
   'DATA'),
  ('base_library.zip',
   'E:\\github\\local_upload\\build\\launcher\\base_library.zip',
   'DATA'),
  ('certifi\\cacert.pem',
   'E:\\github\\local_upload\\venv\\Lib\\site-packages\\certifi\\cacert.pem',
   'DATA'),
  ('certifi\\py.typed',
   'E:\\github\\local_upload\\venv\\Lib\\site-packages\\certifi\\py.typed',
   'DATA')],
 'python312.dll',
 False,
 False,
 False,
 [],
 None,
 None,
 None)
