E:/github/local_upload/frontend/build/windows/x64/runner/Release/data/icudtl.dat
E:/github/local_upload/frontend/build/windows/x64/runner/Release/flutter_windows.dll
E:/github/local_upload/frontend/build/windows/x64/runner/Release/screen_retriever_plugin.dll
E:/github/local_upload/frontend/build/windows/x64/runner/Release/system_tray_plugin.dll
E:/github/local_upload/frontend/build/windows/x64/runner/Release/window_manager_plugin.dll
E:/github/local_upload/frontend/build/windows/x64/runner/Release/data/flutter_assets/AssetManifest.bin
E:/github/local_upload/frontend/build/windows/x64/runner/Release/data/flutter_assets/AssetManifest.json
E:/github/local_upload/frontend/build/windows/x64/runner/Release/data/flutter_assets/assets/icons/app_icon.ico
E:/github/local_upload/frontend/build/windows/x64/runner/Release/data/flutter_assets/FontManifest.json
E:/github/local_upload/frontend/build/windows/x64/runner/Release/data/flutter_assets/fonts/MaterialIcons-Regular.otf
E:/github/local_upload/frontend/build/windows/x64/runner/Release/data/flutter_assets/NativeAssetsManifest.json
E:/github/local_upload/frontend/build/windows/x64/runner/Release/data/flutter_assets/NOTICES.Z
E:/github/local_upload/frontend/build/windows/x64/runner/Release/data/flutter_assets/packages/cupertino_icons/assets/CupertinoIcons.ttf
E:/github/local_upload/frontend/build/windows/x64/runner/Release/data/flutter_assets/packages/window_manager/images/ic_chrome_close.png
E:/github/local_upload/frontend/build/windows/x64/runner/Release/data/flutter_assets/packages/window_manager/images/ic_chrome_maximize.png
E:/github/local_upload/frontend/build/windows/x64/runner/Release/data/flutter_assets/packages/window_manager/images/ic_chrome_minimize.png
E:/github/local_upload/frontend/build/windows/x64/runner/Release/data/flutter_assets/packages/window_manager/images/ic_chrome_unmaximize.png
E:/github/local_upload/frontend/build/windows/x64/runner/Release/data/flutter_assets/shaders/ink_sparkle.frag
E:/github/local_upload/frontend/build/windows/x64/runner/Release/data/app.so