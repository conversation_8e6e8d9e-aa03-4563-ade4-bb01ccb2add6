from flask import Flask, jsonify, request, send_file
from flask_cors import CORS
import asyncio
import threading
import os
import sys
import time
from pathlib import Path
from datetime import datetime
from typing import Dict, List
import json
import logging

from database import Database
from telegram_client import TelegramClientManager
from file_manager import FileManager

# Import Pyrogram exceptions for better error handling
try:
    from pyrogram.errors import (
        SessionPasswordNeeded, PhoneCodeInvalid, 
        PhoneCodeExpired, PasswordHashInvalid, BadRequest, AuthKeyUnregistered,
        FloodWait, PhoneNumberInvalid, ApiIdInvalid, NetworkError
    )
except ImportError:
    # Fallback if specific exceptions aren't available
    SessionPasswordNeeded = Exception
    PhoneCodeInvalid = Exception
    PhoneCodeExpired = Exception
    PasswordHashInvalid = Exception
    BadRequest = Exception
    AuthKeyUnregistered = Exception
    FloodWait = Exception
    PhoneNumberInvalid = Exception
    ApiIdInvalid = Exception
    NetworkError = Exception

def get_app_data_dir():
    """获取应用程序数据目录"""
    app_name = "TelegramUploader"

    if sys.platform == "win32":
        # Windows: 使用 AppData/Local
        base_dir = os.environ.get('LOCALAPPDATA', os.path.expanduser('~'))
        return os.path.join(base_dir, app_name)
    elif sys.platform == "darwin":
        # macOS: 使用 Application Support
        return os.path.expanduser(f'~/Library/Application Support/{app_name}')
    else:
        # Linux: 使用 .local/share
        return os.path.expanduser(f'~/.local/share/{app_name}')

# 初始化应用数据目录（需要在配置日志前创建）
app_data_dir = get_app_data_dir()
os.makedirs(app_data_dir, exist_ok=True)

# 配置日志 - 写入到程序目录下
log_dir = os.path.join(app_data_dir, "logs")
os.makedirs(log_dir, exist_ok=True)

# 创建日志文件路径
log_file = os.path.join(log_dir, f"telegram_uploader_{datetime.now().strftime('%Y%m%d')}.log")

# 配置日志格式
log_formatter = logging.Formatter(
    '%(asctime)s - %(name)s - %(levelname)s - %(message)s',
    datefmt='%Y-%m-%d %H:%M:%S'
)

# 使用RotatingFileHandler实现日志轮转
from logging.handlers import RotatingFileHandler

# 清除现有的处理器，避免重复配置
root_logger = logging.getLogger()
for handler in root_logger.handlers[:]:
    root_logger.removeHandler(handler)

# 配置根日志记录器
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
    datefmt='%Y-%m-%d %H:%M:%S',
    handlers=[
        # 文件处理器 - 写入日志文件，最大10MB，保留5个备份文件
        RotatingFileHandler(
            log_file,
            maxBytes=10*1024*1024,  # 10MB
            backupCount=5,
            encoding='utf-8'
        ),
        # 控制台处理器 - 输出到控制台
        logging.StreamHandler(sys.stdout)
    ],
    force=True  # 强制重新配置
)

logger = logging.getLogger(__name__)

# 记录日志配置信息
logger.info(f"日志目录: {log_dir}")
logger.info(f"日志文件: {log_file}")

# 自定义日志过滤器，屏蔽频繁的轮询请求
class SuppressNoisyRequestsFilter(logging.Filter):
    def filter(self, record):
        # 屏蔽 /api/stats 和 /api/tasks 的 GET 请求日志
        if hasattr(record, 'getMessage'):
            message = record.getMessage()
            if ('GET /api/stats' in message or 'GET /api/tasks' in message) and '200 -' in message:
                return False
        return True

# 应用过滤器到 werkzeug 日志
werkzeug_logger = logging.getLogger('werkzeug')
werkzeug_logger.addFilter(SuppressNoisyRequestsFilter())

# 全局事件循环管理器
class AsyncLoopManager:
    def __init__(self):
        self.loop = None
        self.thread = None
        self._running = False
        
    def start(self):
        """启动后台事件循环"""
        if self._running:
            return
            
        def run_loop():
            self.loop = asyncio.new_event_loop()
            asyncio.set_event_loop(self.loop)
            self.loop.run_forever()
            
        self.thread = threading.Thread(target=run_loop, daemon=True)
        self.thread.start()
        
        # 等待循环启动
        import time
        while self.loop is None:
            time.sleep(0.1)
            
        self._running = True
        logger.info("全局事件循环已启动")
        
    def run_async(self, coro):
        """在全局事件循环中运行协程"""
        if not self._running:
            self.start()
            
        future = asyncio.run_coroutine_threadsafe(coro, self.loop)
        return future.result()
        
    def stop(self):
        """停止事件循环"""
        if self.loop and self._running:
            self.loop.call_soon_threadsafe(self.loop.stop)
            self._running = False
            logger.info("全局事件循环已停止")

# 创建全局事件循环管理器
loop_manager = AsyncLoopManager()

# 创建Flask应用
app = Flask(__name__)
CORS(app)

# 创建sessions目录
sessions_dir = os.path.join(app_data_dir, "sessions")
os.makedirs(sessions_dir, exist_ok=True)

# 数据库路径
db_path = os.path.join(app_data_dir, "telegram_uploader.db")

logger.info(f"应用数据目录: {app_data_dir}")
logger.info(f"数据库路径: {db_path}")
logger.info(f"Session目录: {sessions_dir}")

# 初始化组件
db = Database(db_path)
telegram_manager = TelegramClientManager(db, sessions_dir)
file_manager = FileManager()

# 全局变量
current_tasks = {}
websocket_clients = []

# 创建必要的目录
os.makedirs("sessions", exist_ok=True)

# 启动全局事件循环
loop_manager.start()

# 在应用启动时恢复未完成的任务
def init_task_recovery():
    """初始化任务恢复功能"""
    def delayed_recovery():
        import time
        time.sleep(5)  # 等待5秒让系统稳定
        try:
            # 先自动登录所有有session的账号
            loop_manager.run_async(telegram_manager.auto_login_all_accounts())
            # 然后恢复未完成的任务
            telegram_manager.recover_incomplete_tasks()
        except Exception as e:
            logger.error(f"初始化任务恢复失败: {e}")
    
    # 在后台线程中延迟执行恢复
    recovery_thread = threading.Thread(target=delayed_recovery)
    recovery_thread.daemon = True
    recovery_thread.start()

# 初始化任务恢复
init_task_recovery()

@app.route('/api/accounts', methods=['GET'])
def get_accounts():
    """获取所有账号"""
    try:
        accounts = db.get_accounts()
        return jsonify({
            'success': True,
            'accounts': accounts
        })
    except Exception as e:
        logger.error(f"获取账号失败: {e}")
        return jsonify({
            'success': False,
            'error': str(e)
        }), 500

@app.route('/api/accounts', methods=['POST'])
def add_account():
    """添加账号"""
    try:
        data = request.get_json()
        
        required_fields = ['name', 'api_id', 'api_hash']
        for field in required_fields:
            if field not in data:
                return jsonify({
                    'success': False,
                    'error': f'缺少必要字段: {field}'
                }), 400
        
        account_type = data.get('account_type', 'user')
        
        # 验证账号类型相关的必要字段
        if account_type == 'bot':
            if not data.get('bot_token'):
                return jsonify({
                    'success': False,
                    'error': 'Bot账号需要提供bot_token'
                }), 400
        elif account_type == 'user':
            # 用户账号可以提供phone或session_string，至少有一个
            if not data.get('phone') and not data.get('session_string'):
                return jsonify({
                    'success': False,
                    'error': '用户账号需要提供手机号码或session字符串'
                }), 400
        
        account_id = db.add_account(
            name=data['name'],
            api_id=int(data['api_id']),
            api_hash=data['api_hash'],
            phone=data.get('phone'),
            session_string=data.get('session_string'),
            account_type=account_type,
            bot_token=data.get('bot_token')
        )
        
        return jsonify({
            'success': True,
            'account_id': account_id,
            'message': '账号添加成功'
        })
        
    except Exception as e:
        logger.error(f"添加账号失败: {e}")
        return jsonify({
            'success': False,
            'error': str(e)
        }), 500

@app.route('/api/accounts/<int:account_id>/login', methods=['POST'])
def login_account(account_id):
    """登录账号"""
    try:
        logger.info(f"Login request for account {account_id}")
        # Make JSON parsing optional since login doesn't require request data
        data = request.get_json(silent=True) or {}
        
        # 获取账号信息
        accounts = db.get_accounts()
        account = next((acc for acc in accounts if acc['id'] == account_id), None)
        
        if not account:
            return jsonify({
                'success': False,
                'error': '账号不存在'
            }), 404
        
        # 获取代理配置
        proxy_config = db.get_default_proxy_config()
        
        try:
            client = loop_manager.run_async(
                telegram_manager.create_client(
                    account_name=account['name'],
                    account_id=account_id,
                    api_id=account['api_id'],
                    api_hash=account['api_hash'],
                    phone=account['phone'],
                    session_string=account['session_string'],
                    proxy_config=proxy_config,
                    account_type=account.get('account_type', 'user'),
                    bot_token=account.get('bot_token')
                )
            )
            
            return jsonify({
                'success': True,
                'message': '登录成功',
                'needs_code': False
            })
            
        except (PhoneCodeInvalid, PhoneCodeExpired) as e:
            logger.info(f"需要验证码 (账号ID: {account_id}): {e}")
            return jsonify({
                'success': True,
                'message': '需要验证码',
                'needs_code': True
            })
        except (SessionPasswordNeeded, PasswordHashInvalid) as e:
            logger.info(f"需要密码 (账号ID: {account_id}): {e}")
            return jsonify({
                'success': True,
                'message': '需要密码',
                'needs_password': True
            })
        except FloodWait as e:
            logger.warning(f"账号 {account_id} 发送验证码过于频繁: {e}")
            return jsonify({
                'success': False,
                'error': f'发送验证码过于频繁，请等待 {e.value} 秒后重试',
                'retry_after': e.value
            }), 429
        except (PhoneNumberInvalid, ApiIdInvalid) as e:
            logger.error(f"账号 {account_id} 配置错误: {e}")
            return jsonify({
                'success': False,
                'error': '账号配置错误，请检查手机号码、API ID 或 API Hash'
            }), 400
        except NetworkError as e:
            logger.error(f"账号 {account_id} 网络错误: {e}")
            return jsonify({
                'success': False,
                'error': '网络连接失败，请检查网络连接或代理设置'
            }), 500
        except Exception as e:
            error_msg = str(e)
            
            # 作为后备方案，检查错误消息中的关键字
            if "phone_code" in error_msg.lower() or "验证码" in error_msg or "需要输入验证码" in error_msg:
                # 需要验证码，不清理登录数据
                logger.info(f"账号 {account_id} 需要验证码")
                return jsonify({
                    'success': True,
                    'message': '需要验证码',
                    'needs_code': True
                })
            elif "password" in error_msg.lower() or "密码" in error_msg or "SessionPasswordNeeded" in error_msg or "Two-step verification" in error_msg:
                # 需要密码，不清理登录数据
                logger.info(f"账号 {account_id} 需要密码")
                return jsonify({
                    'success': True,
                    'message': '需要密码',
                    'needs_password': True
                })
            else:
                # 只有真正的错误才清理登录数据并记录为错误
                logger.error(f"登录失败 (账号ID: {account_id}): {error_msg}")
                telegram_manager.cleanup_failed_login(account_id)
                return jsonify({
                    'success': False,
                    'error': f'登录失败: {error_msg}'
                }), 500
            
    except Exception as e:
        logger.exception(f"登录账号失败: {e}")
        return jsonify({
            'success': False,
            'error': f'服务器错误: {str(e)}'
        }), 500

@app.route('/api/accounts/<int:account_id>/verify_code', methods=['POST'])
def verify_code(account_id):
    """验证验证码"""
    try:
        data = request.get_json(force=True)
        if not data:
            return jsonify({
                'success': False,
                'error': '请求数据无效'
            }), 400

        phone_code = data.get('phone_code')

        if not phone_code:
            return jsonify({
                'success': False,
                'error': '验证码不能为空'
            }), 400

        logger.info(f"收到账号 {account_id} 的验证码验证请求: {phone_code}")

        try:
            success = loop_manager.run_async(
                telegram_manager.login_with_code(account_id, phone_code)
            )

            if success:
                return jsonify({
                    'success': True,
                    'message': '验证成功'
                })
            else:
                # 检查是否需要密码
                login_state = telegram_manager.get_login_state(account_id)
                if login_state == 'waiting_password':
                    return jsonify({
                        'success': True,
                        'message': '需要密码',
                        'needs_password': True
                    })
                else:
                    return jsonify({
                        'success': False,
                        'error': '验证码错误或已过期，请重新输入或重新获取验证码'
                    })
        except Exception as inner_e:
            logger.error(f"验证码验证内部异常: {inner_e}")
            return jsonify({
                'success': False,
                'error': f'验证码验证失败: {str(inner_e)}'
            })

    except Exception as e:
        logger.error(f"验证码验证过程中发生异常: {e}")

        # 检查是否需要密码
        login_state = telegram_manager.get_login_state(account_id)
        if login_state == 'waiting_password':
            return jsonify({
                'success': True,
                'message': '需要密码',
                'needs_password': True
            })

        return jsonify({
            'success': False,
            'error': f'验证失败: {str(e)}'
        }), 500

@app.route('/api/accounts/<int:account_id>/resend_code', methods=['POST'])
def resend_code(account_id):
    """重新发送验证码"""
    try:
        logger.info(f"收到账号 {account_id} 的重新发送验证码请求")
        
        try:
            success = loop_manager.run_async(
                telegram_manager.resend_code(account_id)
            )
            
            if success:
                return jsonify({
                    'success': True,
                    'message': '验证码已重新发送'
                })
            else:
                return jsonify({
                    'success': False,
                    'error': '重新发送验证码失败，请重新登录'
                })
        except Exception as inner_e:
            error_msg = str(inner_e)
            if "发送过于频繁" in error_msg and "秒" in error_msg:
                # 解析等待时间
                try:
                    import re
                    wait_time_match = re.search(r'(\d+)\s*秒', error_msg)
                    wait_time = int(wait_time_match.group(1)) if wait_time_match else 60
                    return jsonify({
                        'success': False,
                        'error': error_msg,
                        'retry_after': wait_time
                    }), 429
                except:
                    pass
            
            logger.error(f"重新发送验证码内部异常: {inner_e}")
            return jsonify({
                'success': False,
                'error': f'重新发送验证码失败: {str(inner_e)}'
            })
            
    except Exception as e:
        logger.error(f"重新发送验证码失败: {e}")
        return jsonify({
            'success': False,
            'error': f'重新发送验证码失败: {str(e)}'
        }), 500

@app.route('/api/accounts/<int:account_id>/verify_password', methods=['POST'])
def verify_password(account_id):
    """验证密码"""
    try:
        data = request.get_json(force=True)
        if not data:
            return jsonify({
                'success': False,
                'error': '请求数据无效'
            }), 400
            
        password = data.get('password')
        
        if not password:
            return jsonify({
                'success': False,
                'error': '密码不能为空'
            }), 400
        
        try:
            success = loop_manager.run_async(
                telegram_manager.login_with_password(account_id, password)
            )
            
            if success:
                return jsonify({
                    'success': True,
                    'message': '密码验证成功'
                })
            else:
                return jsonify({
                    'success': False,
                    'error': '密码错误'
                })
        except Exception as inner_e:
            logger.error(f"密码验证内部异常: {inner_e}")
            return jsonify({
                'success': False,
                'error': f'密码验证失败: {str(inner_e)}'
            })
            
    except Exception as e:
        logger.error(f"密码验证失败 (账号ID: {account_id}): {e}")
        return jsonify({
            'success': False,
            'error': f'密码验证失败: {str(e)}'
        }), 500

@app.route('/api/accounts/<int:account_id>/status', methods=['GET'])
def get_account_status(account_id):
    """获取账号登录状态"""
    try:
        # 检查是否有活跃客户端
        client = telegram_manager.clients.get(account_id)
        if client and client.is_connected:
            return jsonify({
                'success': True,
                'status': 'logged_in',
                'message': '已登录'
            })
        
        # 检查登录状态
        login_state = telegram_manager.get_login_state(account_id)
        if login_state:
            state_messages = {
                'waiting_code': '等待验证码',
                'waiting_password': '等待密码'
            }
            return jsonify({
                'success': True,
                'status': login_state,
                'message': state_messages.get(login_state, '登录中')
            })
        
        return jsonify({
            'success': True,
            'status': 'not_logged_in',
            'message': '未登录'
        })
        
    except Exception as e:
        logger.error(f"获取账号状态失败 (账号ID: {account_id}): {e}")
        return jsonify({
            'success': False,
            'error': str(e)
        }), 500

@app.route('/api/accounts/<int:account_id>/logout', methods=['POST'])
def logout_account(account_id):
    """登出账号"""
    try:
        try:
            loop_manager.run_async(
                telegram_manager.disconnect_client(account_id)
            )
            
            return jsonify({
                'success': True,
                'message': '登出成功'
            })
        except Exception as inner_e:
            logger.error(f"登出账号内部异常: {inner_e}")
            return jsonify({
                'success': False,
                'error': f'登出失败: {str(inner_e)}'
            })
            
    except Exception as e:
        logger.error(f"登出账号失败 (账号ID: {account_id}): {e}")
        return jsonify({
            'success': False,
            'error': str(e)
        }), 500

@app.route('/api/accounts/<int:account_id>/dialogs', methods=['GET'])
def get_dialogs(account_id):
    """获取对话列表"""
    try:
        try:
            dialogs = loop_manager.run_async(
                telegram_manager.get_dialogs(account_id)
            )
            
            return jsonify({
                'success': True,
                'dialogs': dialogs
            })
        except Exception as inner_e:
            logger.error(f"获取对话列表内部异常: {inner_e}")
            return jsonify({
                'success': False,
                'error': f'获取对话列表失败: {str(inner_e)}'
            })
            
    except Exception as e:
        logger.error(f"获取对话列表失败: {e}")
        return jsonify({
            'success': False,
            'error': str(e)
        }), 500

@app.route('/api/accounts/<int:account_id>', methods=['DELETE'])
def delete_account(account_id):
    """删除账号"""
    try:
        # 先断开客户端连接
        try:
            loop_manager.run_async(
                telegram_manager.disconnect_client(account_id)
            )
        except Exception as e:
            logger.warning(f"断开账号 {account_id} 连接时出错: {e}")
        
        # 删除账号记录
        db.delete_account(account_id)
        
        return jsonify({
            'success': True,
            'message': '账号删除成功'
        })
        
    except Exception as e:
        logger.error(f"删除账号失败: {e}")
        return jsonify({
            'success': False,
            'error': str(e)
        }), 500

@app.route('/api/accounts/auto-login', methods=['POST'])
def auto_login_all_accounts():
    """自动登录所有有session的账号"""
    try:
        loop_manager.run_async(
            telegram_manager.auto_login_all_accounts()
        )
        
        # 获取登录状态
        logged_in_count = len(telegram_manager.clients)
        
        return jsonify({
            'success': True,
            'message': f'自动登录完成，成功登录 {logged_in_count} 个账号',
            'logged_in_count': logged_in_count
        })
        
    except Exception as e:
        logger.error(f"自动登录失败: {e}")
        return jsonify({
            'success': False,
            'error': str(e)
        }), 500

@app.route('/api/folders/scan', methods=['POST'])
def scan_folder():
    """扫描文件夹"""
    try:
        data = request.get_json()
        folder_path = data.get('folder_path')
        include_subdirs = data.get('include_subdirs', True)
        
        if not folder_path:
            return jsonify({
                'success': False,
                'error': '文件夹路径不能为空'
            }), 400
        
        # 验证文件夹
        validation = file_manager.validate_folder(folder_path)
        if not validation['valid']:
            return jsonify({
                'success': False,
                'error': validation['error']
            }), 400
        
        # 扫描文件夹
        files = file_manager.scan_folder(folder_path, include_subdirs)
        stats = file_manager.get_folder_stats(folder_path)
        
        # 添加到历史记录
        db.add_folder_history(folder_path)
        
        return jsonify({
            'success': True,
            'files': files,
            'stats': stats
        })
        
    except Exception as e:
        logger.error(f"扫描文件夹失败: {e}")
        return jsonify({
            'success': False,
            'error': str(e)
        }), 500

@app.route('/api/folders/history', methods=['GET'])
def get_folder_history():
    """获取文件夹历史记录"""
    try:
        limit = request.args.get('limit', 10, type=int)
        folders = db.get_folder_history(limit)
        
        return jsonify({
            'success': True,
            'folders': folders
        })
        
    except Exception as e:
        logger.error(f"获取文件夹历史失败: {e}")
        return jsonify({
            'success': False,
            'error': str(e)
        }), 500

@app.route('/api/groups/history/<int:account_id>', methods=['GET'])
def get_group_history(account_id):
    """获取群组历史记录"""
    try:
        limit = request.args.get('limit', 10, type=int)
        groups = db.get_group_history(account_id, limit)

        return jsonify({
            'success': True,
            'groups': groups
        })

    except Exception as e:
        logger.error(f"获取群组历史失败: {e}")
        return jsonify({
            'success': False,
            'error': str(e)
        }), 500

@app.route('/api/bots/input-history/<int:account_id>', methods=['GET'])
def get_bot_input_history(account_id):
    """获取机器人输入历史记录"""
    try:
        limit = request.args.get('limit', 10, type=int)
        inputs = db.get_bot_input_history(account_id, limit)

        return jsonify({
            'success': True,
            'inputs': inputs
        })

    except Exception as e:
        logger.error(f"获取机器人输入历史失败: {e}")
        return jsonify({
            'success': False,
            'error': str(e)
        }), 500

@app.route('/api/upload', methods=['POST'])
def start_upload():
    """开始上传任务"""
    try:
        data = request.get_json()
        
        required_fields = ['account_id', 'folder_path', 'target_chat_id']
        for field in required_fields:
            if field not in data:
                return jsonify({
                    'success': False,
                    'error': f'缺少必要字段: {field}'
                }), 400
        
        account_id = int(data['account_id'])
        folder_path = data['folder_path']
        target_chat_id = data['target_chat_id']
        target_chat_name = data.get('target_chat_name')
        
        # 验证文件夹
        validation = file_manager.validate_folder(folder_path)
        if not validation['valid']:
            return jsonify({
                'success': False,
                'error': validation['error']
            }), 400
        
        # 创建任务
        task_id = db.create_upload_task(
            account_id=account_id,
            folder_path=folder_path,
            target_chat_id=target_chat_id,
            target_chat_name=target_chat_name
        )
        
        # 添加到历史记录
        db.add_folder_history(folder_path)

        # 根据账号类型保存不同的历史记录
        account = db.get_account(account_id)
        if account and account.get('account_type') == 'bot':
            # 机器人账号保存输入历史
            db.add_bot_input_history(target_chat_id, account_id)
        else:
            # 用户账号保存群组历史
            db.add_group_history(target_chat_name or target_chat_id, target_chat_id, account_id)
        
        # 在后台线程中执行上传
        def upload_task():
            try:
                loop_manager.run_async(
                    telegram_manager.upload_folder(
                        account_id=account_id,
                        folder_path=folder_path,
                        chat_id=target_chat_id,
                        task_id=task_id
                    )
                )
            except Exception as e:
                logger.error(f"上传任务失败: {e}")
                db.update_task_status(task_id, 'failed')
        
        # 启动后台线程
        upload_thread = threading.Thread(target=upload_task)
        upload_thread.daemon = True
        upload_thread.start()
        
        return jsonify({
            'success': True,
            'task_id': task_id,
            'message': '上传任务已启动'
        })
        
    except Exception as e:
        logger.error(f"启动上传任务失败: {e}")
        return jsonify({
            'success': False,
            'error': str(e)
        }), 500

@app.route('/api/tasks', methods=['GET'])
def get_tasks():
    """获取任务列表"""
    try:
        # 检查是否要获取所有任务（包括历史任务）
        include_history = request.args.get('include_history', 'false').lower() == 'true'
        limit = request.args.get('limit', 100, type=int)
        
        if include_history:
            tasks = db.get_all_tasks(limit)
        else:
            tasks = db.get_active_tasks()
        
        # 添加文件详情
        for task in tasks:
            task['files'] = db.get_task_files(task['id'])
        
        return jsonify({
            'success': True,
            'tasks': tasks
        })
        
    except Exception as e:
        logger.error(f"获取任务列表失败: {e}")
        return jsonify({
            'success': False,
            'error': str(e)
        }), 500

@app.route('/api/tasks/<int:task_id>', methods=['DELETE'])
def delete_task(task_id):
    """删除任务"""
    try:
        # 先检查任务是否存在
        task = db.get_task_by_id(task_id)
        if not task:
            return jsonify({
                'success': False,
                'error': '任务不存在'
            }), 404
        
        # 如果任务正在运行，先停止它
        if task['status'] == 'running':
            # 这里可以添加停止上传任务的逻辑
            db.update_task_status(task_id, 'cancelled')
        
        # 删除任务
        db.delete_task(task_id)
        
        return jsonify({
            'success': True,
            'message': '任务删除成功'
        })
        
    except Exception as e:
        logger.error(f"删除任务失败: {e}")
        return jsonify({
            'success': False,
            'error': str(e)
        }), 500

@app.route('/api/tasks/<int:task_id>/pause', methods=['POST'])
def pause_task(task_id):
    """暂停任务"""
    try:
        task = db.get_task_by_id(task_id)
        if not task:
            return jsonify({
                'success': False,
                'error': '任务不存在'
            }), 404
        
        if task['status'] != 'running':
            return jsonify({
                'success': False,
                'error': '只能暂停正在运行的任务'
            }), 400
        
        # 更新任务状态为暂停
        db.update_task_status(task_id, 'paused')
        
        return jsonify({
            'success': True,
            'message': '任务已暂停'
        })
        
    except Exception as e:
        logger.error(f"暂停任务失败: {e}")
        return jsonify({
            'success': False,
            'error': str(e)
        }), 500

@app.route('/api/tasks/<int:task_id>/resume', methods=['POST'])
def resume_task(task_id):
    """恢复任务"""
    try:
        task = db.get_task_by_id(task_id)
        if not task:
            return jsonify({
                'success': False,
                'error': '任务不存在'
            }), 404
        
        if task['status'] not in ['paused', 'failed']:
            return jsonify({
                'success': False,
                'error': '只能恢复暂停或失败的任务'
            }), 400
        
        # 检查账号是否在线
        account_id = task['account_id']
        client = telegram_manager.clients.get(account_id)
        if not client or not client.is_connected:
            return jsonify({
                'success': False,
                'error': '账号未登录，无法恢复任务'
            }), 400
        
        # 更新任务状态为等待
        db.update_task_status(task_id, 'pending')
        
        # 在后台线程中重新执行上传
        def resume_upload_task():
            try:
                loop_manager.run_async(
                    telegram_manager.resume_upload_task(task_id)
                )
            except Exception as e:
                logger.error(f"恢复上传任务失败: {e}")
                db.update_task_status(task_id, 'failed')
        
        # 启动后台线程
        upload_thread = threading.Thread(target=resume_upload_task)
        upload_thread.daemon = True
        upload_thread.start()
        
        return jsonify({
            'success': True,
            'message': '任务已恢复'
        })
        
    except Exception as e:
        logger.error(f"恢复任务失败: {e}")
        return jsonify({
            'success': False,
            'error': str(e)
        }), 500

@app.route('/api/tasks/<int:task_id>', methods=['GET'])
def get_task_detail(task_id):
    """获取任务详情"""
    try:
        tasks = db.get_active_tasks()
        task = next((t for t in tasks if t['id'] == task_id), None)
        
        if not task:
            return jsonify({
                'success': False,
                'error': '任务不存在'
            }), 404
        
        # 添加文件详情
        task['files'] = db.get_task_files(task_id)
        
        return jsonify({
            'success': True,
            'task': task
        })
        
    except Exception as e:
        logger.error(f"获取任务详情失败: {e}")
        return jsonify({
            'success': False,
            'error': str(e)
        }), 500

@app.route('/api/stats', methods=['GET'])
def get_stats():
    """获取统计信息"""
    try:
        stats = telegram_manager.get_upload_stats()
        
        return jsonify({
            'success': True,
            'stats': stats
        })
        
    except Exception as e:
        logger.error(f"获取统计信息失败: {e}")
        return jsonify({
            'success': False,
            'error': str(e)
        }), 500

@app.route('/api/health', methods=['GET'])
def health_check():
    """健康检查"""
    return jsonify({
        'success': True,
        'message': 'API服务正常',
        'timestamp': datetime.now().isoformat()
    })

@app.route('/api/proxy', methods=['GET'])
def get_proxy_configs():
    """获取代理配置列表"""
    try:
        configs = db.get_proxy_configs()
        return jsonify({
            'success': True,
            'proxies': configs
        })
    except Exception as e:
        logger.error(f"获取代理配置失败: {e}")
        return jsonify({
            'success': False,
            'error': str(e)
        }), 500

@app.route('/api/proxy', methods=['POST'])
def add_proxy_config():
    """添加代理配置"""
    try:
        data = request.get_json()

        logger.info(f"添加代理配置: {data}")
        
        required_fields = ['name', 'proxy_type', 'hostname', 'port']
        for field in required_fields:
            if field not in data:
                return jsonify({
                    'success': False,
                    'error': f'缺少必要字段: {field}'
                }), 400
        
        # 验证代理类型
        valid_types = ['HTTP', 'SOCKS4', 'SOCKS5']
        if data['proxy_type'].upper() not in valid_types:
            return jsonify({
                'success': False,
                'error': f'不支持的代理类型，支持的类型: {", ".join(valid_types)}'
            }), 400
        
        # 验证端口
        try:
            port = int(data['port'])
            if port < 1 or port > 65535:
                raise ValueError()
        except ValueError:
            return jsonify({
                'success': False,
                'error': '端口必须是1-65535之间的数字'
            }), 400
        
        proxy_id = db.add_proxy_config(
            name=data['name'],
            proxy_type=data['proxy_type'].upper(),
            hostname=data['hostname'],
            port=port,
            username=data.get('username'),
            password=data.get('password'),
            enabled=data.get('enabled', False),
            is_default=data.get('is_default', False)
        )
        
        return jsonify({
            'success': True,
            'proxy_id': proxy_id,
            'message': '代理配置添加成功'
        })
        
    except Exception as e:
        logger.error(f"添加代理配置失败: {e}")
        return jsonify({
            'success': False,
            'error': str(e)
        }), 500

@app.route('/api/proxy/<int:proxy_id>', methods=['PUT'])
def update_proxy_config(proxy_id):
    """更新代理配置"""
    try:
        data = request.get_json()
        
        # 如果更新代理类型，验证是否有效
        if 'proxy_type' in data:
            valid_types = ['HTTP', 'SOCKS4', 'SOCKS5']
            if data['proxy_type'].upper() not in valid_types:
                return jsonify({
                    'success': False,
                    'error': f'不支持的代理类型，支持的类型: {", ".join(valid_types)}'
                }), 400
            data['proxy_type'] = data['proxy_type'].upper()
        
        # 如果更新端口，验证范围
        if 'port' in data:
            try:
                port = int(data['port'])
                if port < 1 or port > 65535:
                    raise ValueError()
                data['port'] = port
            except ValueError:
                return jsonify({
                    'success': False,
                    'error': '端口必须是1-65535之间的数字'
                }), 400
        
        db.update_proxy_config(proxy_id, **data)
        
        return jsonify({
            'success': True,
            'message': '代理配置更新成功'
        })
        
    except Exception as e:
        logger.error(f"更新代理配置失败: {e}")
        return jsonify({
            'success': False,
            'error': str(e)
        }), 500

@app.route('/api/proxy/<int:proxy_id>', methods=['DELETE'])
def delete_proxy_config(proxy_id):
    """删除代理配置"""
    try:
        db.delete_proxy_config(proxy_id)
        
        return jsonify({
            'success': True,
            'message': '代理配置删除成功'
        })
        
    except Exception as e:
        logger.error(f"删除代理配置失败: {e}")
        return jsonify({
            'success': False,
            'error': str(e)
        }), 500

@app.route('/api/proxy/test/<int:proxy_id>', methods=['POST'])
def test_proxy_config(proxy_id):
    """测试代理配置"""
    try:
        # 获取代理配置
        configs = db.get_proxy_configs()
        proxy_config = next((config for config in configs if config['id'] == proxy_id), None)
        
        if not proxy_config:
            return jsonify({
                'success': False,
                'error': '代理配置不存在'
            }), 404
        
        # 这里可以添加实际的代理测试逻辑
        # 暂时返回成功，实际使用时可以尝试连接测试
        
        return jsonify({
            'success': True,
            'message': '代理测试成功',
            'latency': 100  # 示例延迟
        })
        
    except Exception as e:
        logger.error(f"测试代理配置失败: {e}")
        return jsonify({
            'success': False,
            'error': str(e)
        }), 500

# 错误处理
@app.errorhandler(404)
def not_found(error):
    return jsonify({
        'success': False,
        'error': '接口不存在'
    }), 404

@app.errorhandler(500)
def internal_error(error):
    return jsonify({
        'success': False,
        'error': '服务器内部错误'
    }), 500

# 设置回调函数
def upload_callback(event):
    """上传回调函数"""
    logger.info(f"上传事件: {event}")
    # 这里可以添加WebSocket推送逻辑

def task_callback(event):
    """任务回调函数"""
    logger.info(f"任务事件: {event}")
    # 这里可以添加WebSocket推送逻辑

# 添加回调
telegram_manager.add_upload_callback(upload_callback)
telegram_manager.add_task_callback(task_callback)

# 应用退出时清理事件循环
import atexit
atexit.register(loop_manager.stop)

if __name__ == '__main__':
    print("启动Telegram上传服务器...")
    print("API地址: http://localhost:8090")
    print("健康检查: http://localhost:8090/api/health")
    print(f"数据目录: {app_data_dir}")
    print(f"日志目录: {log_dir}")

    try:
        # 关闭自动重载功能，避免session文件创建时重启
        # debug=True保留调试信息，use_reloader=False防止文件监控重启
        app.run(host='0.0.0.0', port=8090, debug=True, use_reloader=False, threaded=True)
    finally:
        loop_manager.stop()