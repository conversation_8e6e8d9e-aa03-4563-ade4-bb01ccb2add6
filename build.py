#!/usr/bin/env python3
"""
Telegram Upload Tool 构建脚本
自动构建前后端并打包成单个可执行文件
"""

import subprocess
import sys
import os
import shutil
import zipfile
from pathlib import Path
import time

class BuildManager:
    def __init__(self):
        self.project_root = Path(__file__).parent.absolute()
        self.backend_dir = self.project_root / "backend"
        self.frontend_dir = self.project_root / "frontend"
        self.dist_dir = self.project_root / "dist"
        self.build_dir = self.project_root / "build"
        
        # 检测虚拟环境
        self.venv_dir = self.project_root / "venv"
        self.using_venv = self.venv_dir.exists()
        
        if self.using_venv:
            self.python_exe = self.venv_dir / "Scripts" / "python.exe"
            self.pip_exe = self.venv_dir / "Scripts" / "pip.exe"
        else:
            self.python_exe = "python"
            self.pip_exe = "pip"
        
    def log(self, message):
        """打印带时间戳的日志"""
        timestamp = time.strftime("%Y-%m-%d %H:%M:%S")
        print(f"[{timestamp}] {message}")
    
    def run_command(self, command, cwd=None, shell=True):
        """运行命令并检查结果"""
        self.log(f"执行命令: {command}")
        if cwd:
            self.log(f"工作目录: {cwd}")
        
        try:
            result = subprocess.run(
                command,
                cwd=cwd,
                shell=shell,
                check=True,
                capture_output=True,
                text=True,
                encoding='utf-8'
            )
            self.log("✓ 命令执行成功")
            return True
        except subprocess.CalledProcessError as e:
            self.log(f"❌ 命令执行失败: {e}")
            self.log(f"错误输出: {e.stderr}")
            return False
    
    def setup_venv(self):
        """设置虚拟环境"""
        if not self.venv_dir.exists():
            self.log("🔧 创建虚拟环境...")
            if not self.run_command("python -m venv venv", cwd=self.project_root):
                return False
            self.using_venv = True
            self.python_exe = self.venv_dir / "Scripts" / "python.exe"
            self.pip_exe = self.venv_dir / "Scripts" / "pip.exe"
        
        self.log(f"✓ 使用虚拟环境: {self.venv_dir}")
        return True
    
    def clean_build(self):
        """清理构建目录"""
        self.log("🧹 清理构建目录...")
        
        # 清理后端构建目录
        backend_dist = self.backend_dir / "dist"
        backend_build = self.backend_dir / "build"
        
        dirs_to_clean = [backend_dist, backend_build, self.dist_dir, self.build_dir]
        
        for dir_path in dirs_to_clean:
            if dir_path.exists():
                try:
                    shutil.rmtree(dir_path)
                    self.log(f"✓ 删除目录: {dir_path}")
                except OSError as e:
                    self.log(f"⚠️ 无法删除目录 {dir_path}: {e}")
                    # 尝试强制删除
                    try:
                        self.run_command(f'rmdir /s /q "{dir_path}"', shell=True)
                        self.log(f"✓ 强制删除目录: {dir_path}")
                    except Exception as e2:
                        self.log(f"❌ 强制删除失败 {dir_path}: {e2}")
                        # 继续执行，不中断构建过程
        
        # 创建构建目录
        try:
            self.build_dir.mkdir(exist_ok=True)
            self.dist_dir.mkdir(exist_ok=True)
            self.log("✓ 创建构建目录")
        except Exception as e:
            self.log(f"❌ 创建构建目录失败: {e}")
            return False
        
        return True
    
    def install_backend_dependencies(self):
        """安装后端依赖"""
        self.log("📦 安装后端依赖...")
        
        # 使用虚拟环境的 pip
        return self.run_command(
            f'"{self.pip_exe}" install -r requirements-build.txt',
            cwd=self.backend_dir
        )
    
    def build_backend(self):
        """构建后端应用"""
        self.log("🔨 构建后端应用...")
        
        # 使用虚拟环境的 python
        success = self.run_command(
            f'"{self.python_exe}" -m PyInstaller backend.spec --clean',
            cwd=self.backend_dir
        )
        
        if success:
            # 检查构建结果
            backend_exe = self.backend_dir / "dist" / "telegram_backend" / "telegram_backend.exe"
            if backend_exe.exists():
                self.log("✓ 后端构建成功")
                return True
            else:
                self.log("❌ 后端可执行文件未找到")
                return False
        return False
    
    def build_frontend(self):
        """构建前端应用"""
        self.log("🖥️ 构建前端应用...")
        
        # Flutter clean
        if not self.run_command("flutter clean", cwd=self.frontend_dir):
            return False
        
        # Flutter pub get
        if not self.run_command("flutter pub get", cwd=self.frontend_dir):
            return False
        
        # Flutter build windows
        success = self.run_command(
            "flutter build windows --release",
            cwd=self.frontend_dir
        )
        
        if success:
            # 检查构建结果
            frontend_exe = (self.frontend_dir / "build" / "windows" / "x64" / 
                          "runner" / "Release" / "telegram_uploader.exe")
            if frontend_exe.exists():
                self.log("✓ 前端构建成功")
                return True
            else:
                self.log("❌ 前端可执行文件未找到")
                return False
        return False
    
    def build_launcher(self):
        """构建启动器"""
        self.log("🚀 构建启动器...")
        
        # 创建启动器的 spec 文件
        launcher_spec = self.project_root / "launcher.spec"
        spec_content = '''# -*- mode: python ; coding: utf-8 -*-

block_cipher = None

a = Analysis(
    ['launcher.py'],
    pathex=[],
    binaries=[],
    datas=[
        ('backend/dist/telegram_backend', 'backend/dist/telegram_backend'),
        ('frontend/build/windows/x64/runner/Release', 'frontend/build/windows/x64/runner/Release'),
    ],
    hiddenimports=['requests'],
    hookspath=[],
    hooksconfig={},
    runtime_hooks=[],
    excludes=[],
    win_no_prefer_redirects=False,
    win_private_assemblies=False,
    cipher=block_cipher,
    noarchive=False,
)

pyz = PYZ(a.pure, a.zipped_data, cipher=block_cipher)

exe = EXE(
    pyz,
    a.scripts,
    a.binaries,
    a.zipfiles,
    a.datas,
    [],
    name='TelegramUploadTool',
    debug=False,
    bootloader_ignore_signals=False,
    strip=False,
    upx=True,
    upx_exclude=[],
    runtime_tmpdir=None,
    console=True,
    disable_windowed_traceback=False,
    argv_emulation=False,
    target_arch=None,
    codesign_identity=None,
    entitlements_file=None,
    icon='frontend/assets/icons/app_icon.ico'
)
'''
        
        with open(launcher_spec, 'w', encoding='utf-8') as f:
            f.write(spec_content)
        
        # 构建启动器，使用虚拟环境的 python
        success = self.run_command(
            f'"{self.python_exe}" -m PyInstaller launcher.spec --clean',
            cwd=self.project_root
        )
        
        # 清理 spec 文件
        if launcher_spec.exists():
            launcher_spec.unlink()
        
        if success:
            launcher_exe = self.project_root / "dist" / "TelegramUploadTool.exe"
            if launcher_exe.exists():
                self.log("✓ 启动器构建成功")
                return True
            else:
                self.log("❌ 启动器可执行文件未找到")
                return False
        return False
    
    def create_portable_package(self):
        """创建便携版打包"""
        self.log("📦 创建便携版打包...")
        
        portable_dir = self.build_dir / "TelegramUploadTool_Portable"
        portable_dir.mkdir(exist_ok=True)
        
        # 复制后端文件
        backend_src = self.backend_dir / "dist" / "telegram_backend"
        backend_dst = portable_dir / "backend" / "dist" / "telegram_backend"
        if backend_src.exists():
            shutil.copytree(backend_src, backend_dst)
            self.log("✓ 复制后端文件")
        
        # 复制前端文件
        frontend_src = self.frontend_dir / "build" / "windows" / "x64" / "runner" / "Release"
        frontend_dst = portable_dir / "frontend" / "build" / "windows" / "x64" / "runner" / "Release"
        if frontend_src.exists():
            shutil.copytree(frontend_src, frontend_dst)
            self.log("✓ 复制前端文件")
        
        # 复制启动器
        launcher_src = self.project_root / "launcher.py"
        launcher_dst = portable_dir / "launcher.py"
        if launcher_src.exists():
            shutil.copy2(launcher_src, launcher_dst)
            self.log("✓ 复制启动器脚本")
        
        # 创建批处理启动文件
        batch_file = portable_dir / "启动应用.bat"
        batch_content = '''@echo off
chcp 65001 > nul
echo 启动 Telegram Upload Tool...
echo.
echo 正在检查环境...

:: 检查是否安装了Python
python --version >nul 2>&1
if errorlevel 1 (
    echo ❌ 未找到Python环境，请安装Python 3.8或更高版本
    echo 下载地址: https://www.python.org/downloads/
    pause
    exit /b 1
)

:: 检查是否安装了requests模块
python -c "import requests" >nul 2>&1
if errorlevel 1 (
    echo ⚠️ 未找到requests模块，正在安装...
    pip install requests
    if errorlevel 1 (
        echo ❌ 无法安装requests模块
        pause
        exit /b 1
    )
)

echo ✓ 环境检查完成
echo.
python launcher.py
pause
'''
        with open(batch_file, 'w', encoding='utf-8') as f:
            f.write(batch_content)
        
        # 创建说明文件
        readme_file = portable_dir / "README.txt"
        readme_content = '''Telegram Upload Tool 便携版
============================

使用说明：
1. 双击 "启动应用.bat" 启动应用
2. 或者运行 "python launcher.py"

系统要求：
- Windows 10 或更高版本
- Python 3.8 或更高版本
- 网络连接

安装说明：
如果系统中没有Python环境，请先安装Python：
https://www.python.org/downloads/

注意事项：
- 首次运行可能需要一些时间来初始化
- 确保网络连接正常
- 关闭前端窗口将自动退出整个应用
- 如果遇到权限问题，请以管理员身份运行

故障排除：
- 如果启动失败，请检查Python是否正确安装
- 确保防火墙允许应用访问网络
- 检查杀毒软件是否误报并添加白名单
'''
        with open(readme_file, 'w', encoding='utf-8') as f:
            f.write(readme_content)
        
        # 创建 ZIP 包
        zip_file = self.dist_dir / "TelegramUploadTool_Portable.zip"
        with zipfile.ZipFile(zip_file, 'w', zipfile.ZIP_DEFLATED) as zf:
            for file_path in portable_dir.rglob('*'):
                if file_path.is_file():
                    arc_path = file_path.relative_to(portable_dir)
                    zf.write(file_path, arc_path)
        
        self.log(f"✓ 便携版打包完成: {zip_file}")
        return True
    
    def build_all(self):
        """执行完整构建流程"""
        self.log("🚀 开始构建 Telegram Upload Tool")
        self.log("=" * 60)
        
        # 检查虚拟环境状态
        if self.using_venv:
            self.log(f"✓ 使用虚拟环境: {self.venv_dir}")
        else:
            self.log("⚠️ 未使用虚拟环境，建议先运行: python -m venv venv")
        
        steps = [
            ("设置虚拟环境", self.setup_venv),
            ("清理构建目录", self.clean_build),
            ("安装后端依赖", self.install_backend_dependencies),
            ("构建后端应用", self.build_backend),
            ("构建前端应用", self.build_frontend),
            ("构建启动器", self.build_launcher),
            ("创建便携版打包", self.create_portable_package),
        ]
        
        for step_name, step_func in steps:
            self.log(f"\n📋 步骤: {step_name}")
            if not step_func():
                self.log(f"❌ 步骤失败: {step_name}")
                self.log("❌ 构建过程中止")
                return False
            self.log(f"✅ 步骤完成: {step_name}")
        
        self.log("\n🎉 构建完成！")
        self.log("=" * 60)
        self.log(f"📁 可执行文件: {self.project_root / 'dist' / 'TelegramUploadTool.exe'}")
        self.log(f"📁 便携版打包: {self.project_root / 'dist' / 'TelegramUploadTool_Portable.zip'}")
        
        # 显示文件大小信息
        exe_file = self.project_root / "dist" / "TelegramUploadTool.exe"
        zip_file = self.project_root / "dist" / "TelegramUploadTool_Portable.zip"
        
        if exe_file.exists():
            size_mb = exe_file.stat().st_size / (1024 * 1024)
            self.log(f"📊 可执行文件大小: {size_mb:.1f} MB")
        
        if zip_file.exists():
            size_mb = zip_file.stat().st_size / (1024 * 1024)
            self.log(f"📊 便携版大小: {size_mb:.1f} MB")
        
        return True

if __name__ == "__main__":
    builder = BuildManager()
    if not builder.build_all():
        sys.exit(1) 