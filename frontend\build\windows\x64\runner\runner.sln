﻿
Microsoft Visual Studio Solution File, Format Version 12.00
# Visual Studio Version 17
Project("{8BC9CEB8-8B4A-11D0-8D11-00A0C91BC942}") = "ALL_BUILD", "ALL_BUILD.vcxproj", "{E76C3D71-5F07-3220-9006-1D8710725D73}"
	ProjectSection(ProjectDependencies) = postProject
		{391C6CE4-8939-3867-A1A5-D2E4CEF587B4} = {391C6CE4-8939-3867-A1A5-D2E4CEF587B4}
		{7EC7352C-23C8-345F-A815-C3024B015EED} = {7EC7352C-23C8-345F-A815-C3024B015EED}
	EndProjectSection
EndProject
Project("{8BC9CEB8-8B4A-11D0-8D11-00A0C91BC942}") = "INSTALL", "INSTALL.vcxproj", "{28E938D9-5DE5-3671-AC30-1B3C045F93CB}"
	ProjectSection(ProjectDependencies) = postProject
		{E76C3D71-5F07-3220-9006-1D8710725D73} = {E76C3D71-5F07-3220-9006-1D8710725D73}
		{391C6CE4-8939-3867-A1A5-D2E4CEF587B4} = {391C6CE4-8939-3867-A1A5-D2E4CEF587B4}
	EndProjectSection
EndProject
Project("{8BC9CEB8-8B4A-11D0-8D11-00A0C91BC942}") = "ZERO_CHECK", "..\\ZERO_CHECK.vcxproj", "{391C6CE4-8939-3867-A1A5-D2E4CEF587B4}"
	ProjectSection(ProjectDependencies) = postProject
	EndProjectSection
EndProject
Project("{8BC9CEB8-8B4A-11D0-8D11-00A0C91BC942}") = "flutter_assemble", "..\flutter\flutter_assemble.vcxproj", "{5DD86A62-ADBC-3EBA-ABFA-8C3E319ECBD5}"
	ProjectSection(ProjectDependencies) = postProject
		{391C6CE4-8939-3867-A1A5-D2E4CEF587B4} = {391C6CE4-8939-3867-A1A5-D2E4CEF587B4}
	EndProjectSection
EndProject
Project("{8BC9CEB8-8B4A-11D0-8D11-00A0C91BC942}") = "flutter_wrapper_app", "..\flutter\flutter_wrapper_app.vcxproj", "{B9F51CD8-53DB-38EE-B913-BBC87D480554}"
	ProjectSection(ProjectDependencies) = postProject
		{391C6CE4-8939-3867-A1A5-D2E4CEF587B4} = {391C6CE4-8939-3867-A1A5-D2E4CEF587B4}
		{5DD86A62-ADBC-3EBA-ABFA-8C3E319ECBD5} = {5DD86A62-ADBC-3EBA-ABFA-8C3E319ECBD5}
	EndProjectSection
EndProject
Project("{8BC9CEB8-8B4A-11D0-8D11-00A0C91BC942}") = "flutter_wrapper_plugin", "..\flutter\flutter_wrapper_plugin.vcxproj", "{32AAA8A5-8AD3-344A-AB9F-407C0BE20D66}"
	ProjectSection(ProjectDependencies) = postProject
		{391C6CE4-8939-3867-A1A5-D2E4CEF587B4} = {391C6CE4-8939-3867-A1A5-D2E4CEF587B4}
		{5DD86A62-ADBC-3EBA-ABFA-8C3E319ECBD5} = {5DD86A62-ADBC-3EBA-ABFA-8C3E319ECBD5}
	EndProjectSection
EndProject
Project("{8BC9CEB8-8B4A-11D0-8D11-00A0C91BC942}") = "screen_retriever_plugin", "..\plugins\screen_retriever\screen_retriever_plugin.vcxproj", "{223946B4-3BF0-3311-AA5F-1FBF612943AB}"
	ProjectSection(ProjectDependencies) = postProject
		{391C6CE4-8939-3867-A1A5-D2E4CEF587B4} = {391C6CE4-8939-3867-A1A5-D2E4CEF587B4}
		{5DD86A62-ADBC-3EBA-ABFA-8C3E319ECBD5} = {5DD86A62-ADBC-3EBA-ABFA-8C3E319ECBD5}
		{32AAA8A5-8AD3-344A-AB9F-407C0BE20D66} = {32AAA8A5-8AD3-344A-AB9F-407C0BE20D66}
	EndProjectSection
EndProject
Project("{8BC9CEB8-8B4A-11D0-8D11-00A0C91BC942}") = "system_tray_plugin", "..\plugins\system_tray\system_tray_plugin.vcxproj", "{276E8E54-89C2-3BB4-A243-3BFD49A72710}"
	ProjectSection(ProjectDependencies) = postProject
		{391C6CE4-8939-3867-A1A5-D2E4CEF587B4} = {391C6CE4-8939-3867-A1A5-D2E4CEF587B4}
		{5DD86A62-ADBC-3EBA-ABFA-8C3E319ECBD5} = {5DD86A62-ADBC-3EBA-ABFA-8C3E319ECBD5}
		{32AAA8A5-8AD3-344A-AB9F-407C0BE20D66} = {32AAA8A5-8AD3-344A-AB9F-407C0BE20D66}
	EndProjectSection
EndProject
Project("{8BC9CEB8-8B4A-11D0-8D11-00A0C91BC942}") = "telegram_uploader", "telegram_uploader.vcxproj", "{7EC7352C-23C8-345F-A815-C3024B015EED}"
	ProjectSection(ProjectDependencies) = postProject
		{391C6CE4-8939-3867-A1A5-D2E4CEF587B4} = {391C6CE4-8939-3867-A1A5-D2E4CEF587B4}
		{5DD86A62-ADBC-3EBA-ABFA-8C3E319ECBD5} = {5DD86A62-ADBC-3EBA-ABFA-8C3E319ECBD5}
		{B9F51CD8-53DB-38EE-B913-BBC87D480554} = {B9F51CD8-53DB-38EE-B913-BBC87D480554}
		{223946B4-3BF0-3311-AA5F-1FBF612943AB} = {223946B4-3BF0-3311-AA5F-1FBF612943AB}
		{276E8E54-89C2-3BB4-A243-3BFD49A72710} = {276E8E54-89C2-3BB4-A243-3BFD49A72710}
		{AAC3A724-1717-3171-8B72-BF156847D8DB} = {AAC3A724-1717-3171-8B72-BF156847D8DB}
	EndProjectSection
EndProject
Project("{8BC9CEB8-8B4A-11D0-8D11-00A0C91BC942}") = "window_manager_plugin", "..\plugins\window_manager\window_manager_plugin.vcxproj", "{AAC3A724-1717-3171-8B72-BF156847D8DB}"
	ProjectSection(ProjectDependencies) = postProject
		{391C6CE4-8939-3867-A1A5-D2E4CEF587B4} = {391C6CE4-8939-3867-A1A5-D2E4CEF587B4}
		{5DD86A62-ADBC-3EBA-ABFA-8C3E319ECBD5} = {5DD86A62-ADBC-3EBA-ABFA-8C3E319ECBD5}
		{32AAA8A5-8AD3-344A-AB9F-407C0BE20D66} = {32AAA8A5-8AD3-344A-AB9F-407C0BE20D66}
	EndProjectSection
EndProject
Global
	GlobalSection(SolutionConfigurationPlatforms) = preSolution
		Debug|x64 = Debug|x64
		Profile|x64 = Profile|x64
		Release|x64 = Release|x64
	EndGlobalSection
	GlobalSection(ProjectConfigurationPlatforms) = postSolution
		{E76C3D71-5F07-3220-9006-1D8710725D73}.Debug|x64.ActiveCfg = Debug|x64
		{E76C3D71-5F07-3220-9006-1D8710725D73}.Debug|x64.Build.0 = Debug|x64
		{E76C3D71-5F07-3220-9006-1D8710725D73}.Profile|x64.ActiveCfg = Profile|x64
		{E76C3D71-5F07-3220-9006-1D8710725D73}.Profile|x64.Build.0 = Profile|x64
		{E76C3D71-5F07-3220-9006-1D8710725D73}.Release|x64.ActiveCfg = Release|x64
		{E76C3D71-5F07-3220-9006-1D8710725D73}.Release|x64.Build.0 = Release|x64
		{28E938D9-5DE5-3671-AC30-1B3C045F93CB}.Debug|x64.ActiveCfg = Debug|x64
		{28E938D9-5DE5-3671-AC30-1B3C045F93CB}.Profile|x64.ActiveCfg = Profile|x64
		{28E938D9-5DE5-3671-AC30-1B3C045F93CB}.Release|x64.ActiveCfg = Release|x64
		{391C6CE4-8939-3867-A1A5-D2E4CEF587B4}.Debug|x64.ActiveCfg = Debug|x64
		{391C6CE4-8939-3867-A1A5-D2E4CEF587B4}.Debug|x64.Build.0 = Debug|x64
		{391C6CE4-8939-3867-A1A5-D2E4CEF587B4}.Profile|x64.ActiveCfg = Profile|x64
		{391C6CE4-8939-3867-A1A5-D2E4CEF587B4}.Profile|x64.Build.0 = Profile|x64
		{391C6CE4-8939-3867-A1A5-D2E4CEF587B4}.Release|x64.ActiveCfg = Release|x64
		{391C6CE4-8939-3867-A1A5-D2E4CEF587B4}.Release|x64.Build.0 = Release|x64
		{5DD86A62-ADBC-3EBA-ABFA-8C3E319ECBD5}.Debug|x64.ActiveCfg = Debug|x64
		{5DD86A62-ADBC-3EBA-ABFA-8C3E319ECBD5}.Debug|x64.Build.0 = Debug|x64
		{5DD86A62-ADBC-3EBA-ABFA-8C3E319ECBD5}.Profile|x64.ActiveCfg = Profile|x64
		{5DD86A62-ADBC-3EBA-ABFA-8C3E319ECBD5}.Profile|x64.Build.0 = Profile|x64
		{5DD86A62-ADBC-3EBA-ABFA-8C3E319ECBD5}.Release|x64.ActiveCfg = Release|x64
		{5DD86A62-ADBC-3EBA-ABFA-8C3E319ECBD5}.Release|x64.Build.0 = Release|x64
		{B9F51CD8-53DB-38EE-B913-BBC87D480554}.Debug|x64.ActiveCfg = Debug|x64
		{B9F51CD8-53DB-38EE-B913-BBC87D480554}.Debug|x64.Build.0 = Debug|x64
		{B9F51CD8-53DB-38EE-B913-BBC87D480554}.Profile|x64.ActiveCfg = Profile|x64
		{B9F51CD8-53DB-38EE-B913-BBC87D480554}.Profile|x64.Build.0 = Profile|x64
		{B9F51CD8-53DB-38EE-B913-BBC87D480554}.Release|x64.ActiveCfg = Release|x64
		{B9F51CD8-53DB-38EE-B913-BBC87D480554}.Release|x64.Build.0 = Release|x64
		{32AAA8A5-8AD3-344A-AB9F-407C0BE20D66}.Debug|x64.ActiveCfg = Debug|x64
		{32AAA8A5-8AD3-344A-AB9F-407C0BE20D66}.Debug|x64.Build.0 = Debug|x64
		{32AAA8A5-8AD3-344A-AB9F-407C0BE20D66}.Profile|x64.ActiveCfg = Profile|x64
		{32AAA8A5-8AD3-344A-AB9F-407C0BE20D66}.Profile|x64.Build.0 = Profile|x64
		{32AAA8A5-8AD3-344A-AB9F-407C0BE20D66}.Release|x64.ActiveCfg = Release|x64
		{32AAA8A5-8AD3-344A-AB9F-407C0BE20D66}.Release|x64.Build.0 = Release|x64
		{223946B4-3BF0-3311-AA5F-1FBF612943AB}.Debug|x64.ActiveCfg = Debug|x64
		{223946B4-3BF0-3311-AA5F-1FBF612943AB}.Debug|x64.Build.0 = Debug|x64
		{223946B4-3BF0-3311-AA5F-1FBF612943AB}.Profile|x64.ActiveCfg = Profile|x64
		{223946B4-3BF0-3311-AA5F-1FBF612943AB}.Profile|x64.Build.0 = Profile|x64
		{223946B4-3BF0-3311-AA5F-1FBF612943AB}.Release|x64.ActiveCfg = Release|x64
		{223946B4-3BF0-3311-AA5F-1FBF612943AB}.Release|x64.Build.0 = Release|x64
		{276E8E54-89C2-3BB4-A243-3BFD49A72710}.Debug|x64.ActiveCfg = Debug|x64
		{276E8E54-89C2-3BB4-A243-3BFD49A72710}.Debug|x64.Build.0 = Debug|x64
		{276E8E54-89C2-3BB4-A243-3BFD49A72710}.Profile|x64.ActiveCfg = Profile|x64
		{276E8E54-89C2-3BB4-A243-3BFD49A72710}.Profile|x64.Build.0 = Profile|x64
		{276E8E54-89C2-3BB4-A243-3BFD49A72710}.Release|x64.ActiveCfg = Release|x64
		{276E8E54-89C2-3BB4-A243-3BFD49A72710}.Release|x64.Build.0 = Release|x64
		{7EC7352C-23C8-345F-A815-C3024B015EED}.Debug|x64.ActiveCfg = Debug|x64
		{7EC7352C-23C8-345F-A815-C3024B015EED}.Debug|x64.Build.0 = Debug|x64
		{7EC7352C-23C8-345F-A815-C3024B015EED}.Profile|x64.ActiveCfg = Profile|x64
		{7EC7352C-23C8-345F-A815-C3024B015EED}.Profile|x64.Build.0 = Profile|x64
		{7EC7352C-23C8-345F-A815-C3024B015EED}.Release|x64.ActiveCfg = Release|x64
		{7EC7352C-23C8-345F-A815-C3024B015EED}.Release|x64.Build.0 = Release|x64
		{AAC3A724-1717-3171-8B72-BF156847D8DB}.Debug|x64.ActiveCfg = Debug|x64
		{AAC3A724-1717-3171-8B72-BF156847D8DB}.Debug|x64.Build.0 = Debug|x64
		{AAC3A724-1717-3171-8B72-BF156847D8DB}.Profile|x64.ActiveCfg = Profile|x64
		{AAC3A724-1717-3171-8B72-BF156847D8DB}.Profile|x64.Build.0 = Profile|x64
		{AAC3A724-1717-3171-8B72-BF156847D8DB}.Release|x64.ActiveCfg = Release|x64
		{AAC3A724-1717-3171-8B72-BF156847D8DB}.Release|x64.Build.0 = Release|x64
	EndGlobalSection
	GlobalSection(ExtensibilityGlobals) = postSolution
		SolutionGuid = {E2D5CFD5-8C36-3E7A-BC45-D0438687FC9A}
	EndGlobalSection
	GlobalSection(ExtensibilityAddIns) = postSolution
	EndGlobalSection
EndGlobal
