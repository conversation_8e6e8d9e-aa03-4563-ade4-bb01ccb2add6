﻿<?xml version="1.0" encoding="utf-8"?>
<Project ToolsVersion="17.0" xmlns="http://schemas.microsoft.com/developer/msbuild/2003">
  <ItemGroup>
    <ClCompile Include="E:\github\local_upload\frontend\windows\flutter\ephemeral\cpp_client_wrapper\core_implementations.cc">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="E:\github\local_upload\frontend\windows\flutter\ephemeral\cpp_client_wrapper\standard_codec.cc">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="E:\github\local_upload\frontend\windows\flutter\ephemeral\cpp_client_wrapper\flutter_engine.cc">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="E:\github\local_upload\frontend\windows\flutter\ephemeral\cpp_client_wrapper\flutter_view_controller.cc">
      <Filter>Source Files</Filter>
    </ClCompile>
  </ItemGroup>
  <ItemGroup>
    <CustomBuild Include="E:\github\local_upload\frontend\windows\flutter\CMakeLists.txt" />
  </ItemGroup>
  <ItemGroup>
    <Filter Include="CMake Rules">
      <UniqueIdentifier>{BDF2B195-9A96-320A-9F29-3F2D85FD76A3}</UniqueIdentifier>
    </Filter>
    <Filter Include="Source Files">
      <UniqueIdentifier>{096B0447-651C-39D1-803D-35B402FCF074}</UniqueIdentifier>
    </Filter>
  </ItemGroup>
</Project>
