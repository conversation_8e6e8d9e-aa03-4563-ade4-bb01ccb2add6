#!/usr/bin/env python3
# -*- coding: utf-8 -*-

import os
import sys
from datetime import datetime

def get_app_data_dir():
    """获取应用程序数据目录"""
    app_name = "TelegramUploader"

    if sys.platform == "win32":
        # Windows: 使用 AppData/Local
        base_dir = os.environ.get('LOCALAPPDATA', os.path.expanduser('~'))
        return os.path.join(base_dir, app_name)
    elif sys.platform == "darwin":
        # macOS: 使用 Application Support
        return os.path.expanduser(f'~/Library/Application Support/{app_name}')
    else:
        # Linux: 使用 .local/share
        return os.path.expanduser(f'~/.local/share/{app_name}')

def view_today_log():
    """查看今天的日志文件"""
    app_data_dir = get_app_data_dir()
    log_dir = os.path.join(app_data_dir, "logs")
    log_file = os.path.join(log_dir, f"telegram_uploader_{datetime.now().strftime('%Y%m%d')}.log")
    
    print(f"日志文件路径: {log_file}")
    
    if not os.path.exists(log_file):
        print("日志文件不存在")
        return
    
    file_size = os.path.getsize(log_file)
    print(f"文件大小: {file_size} 字节")
    
    if file_size == 0:
        print("日志文件为空")
        return
    
    print("\n=== 日志内容 ===")
    try:
        with open(log_file, 'r', encoding='utf-8') as f:
            content = f.read()
            print(content)
    except Exception as e:
        print(f"读取日志文件失败: {e}")

def list_log_files():
    """列出所有日志文件"""
    app_data_dir = get_app_data_dir()
    log_dir = os.path.join(app_data_dir, "logs")
    
    print(f"日志目录: {log_dir}")
    
    if not os.path.exists(log_dir):
        print("日志目录不存在")
        return
    
    print("\n=== 日志文件列表 ===")
    for file in os.listdir(log_dir):
        if file.endswith('.log'):
            file_path = os.path.join(log_dir, file)
            file_size = os.path.getsize(file_path)
            mod_time = datetime.fromtimestamp(os.path.getmtime(file_path))
            print(f"{file} - {file_size} 字节 - 修改时间: {mod_time}")

if __name__ == "__main__":
    print("=== Telegram Uploader 日志查看器 ===\n")
    
    list_log_files()
    print()
    view_today_log()
