([('telegram_backend.exe',
   'E:\\github\\local_upload\\backend\\build\\backend\\telegram_backend.exe',
   'EXECUTABLE'),
  ('python312.dll', 'C:\\Python312\\python312.dll', 'BINARY'),
  ('select.pyd', 'C:\\Python312\\DLLs\\select.pyd', 'EXTENSION'),
  ('_hashlib.pyd', 'C:\\Python312\\DLLs\\_hashlib.pyd', 'EXTENSION'),
  ('_lzma.pyd', 'C:\\Python312\\DLLs\\_lzma.pyd', 'EXTENSION'),
  ('_bz2.pyd', 'C:\\Python312\\DLLs\\_bz2.pyd', 'EXTENSION'),
  ('pyexpat.pyd', 'C:\\Python312\\DLLs\\pyexpat.pyd', 'EXTENSION'),
  ('_ssl.pyd', 'C:\\Python312\\DLLs\\_ssl.pyd', 'EXTENSION'),
  ('unicodedata.pyd', 'C:\\Python312\\DLLs\\unicodedata.pyd', 'EXTENSION'),
  ('_decimal.pyd', 'C:\\Python312\\DLLs\\_decimal.pyd', 'EXTENSION'),
  ('_multiprocessing.pyd',
   'C:\\Python312\\DLLs\\_multiprocessing.pyd',
   'EXTENSION'),
  ('_socket.pyd', 'C:\\Python312\\DLLs\\_socket.pyd', 'EXTENSION'),
  ('_ctypes.pyd', 'C:\\Python312\\DLLs\\_ctypes.pyd', 'EXTENSION'),
  ('_queue.pyd', 'C:\\Python312\\DLLs\\_queue.pyd', 'EXTENSION'),
  ('_sqlite3.pyd', 'C:\\Python312\\DLLs\\_sqlite3.pyd', 'EXTENSION'),
  ('tgcrypto.cp312-win_amd64.pyd',
   'E:\\github\\local_upload\\venv\\Lib\\site-packages\\tgcrypto.cp312-win_amd64.pyd',
   'EXTENSION'),
  ('_wmi.pyd', 'C:\\Python312\\DLLs\\_wmi.pyd', 'EXTENSION'),
  ('_uuid.pyd', 'C:\\Python312\\DLLs\\_uuid.pyd', 'EXTENSION'),
  ('_overlapped.pyd', 'C:\\Python312\\DLLs\\_overlapped.pyd', 'EXTENSION'),
  ('_asyncio.pyd', 'C:\\Python312\\DLLs\\_asyncio.pyd', 'EXTENSION'),
  ('markupsafe\\_speedups.cp312-win_amd64.pyd',
   'E:\\github\\local_upload\\venv\\Lib\\site-packages\\markupsafe\\_speedups.cp312-win_amd64.pyd',
   'EXTENSION'),
  ('api-ms-win-crt-process-l1-1-0.dll',
   'C:\\WINDOWS\\system32\\api-ms-win-crt-process-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-crt-stdio-l1-1-0.dll',
   'C:\\WINDOWS\\system32\\api-ms-win-crt-stdio-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-crt-math-l1-1-0.dll',
   'C:\\WINDOWS\\system32\\api-ms-win-crt-math-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-crt-time-l1-1-0.dll',
   'C:\\WINDOWS\\system32\\api-ms-win-crt-time-l1-1-0.dll',
   'BINARY'),
  ('VCRUNTIME140.dll', 'C:\\Python312\\VCRUNTIME140.dll', 'BINARY'),
  ('api-ms-win-crt-conio-l1-1-0.dll',
   'C:\\WINDOWS\\system32\\api-ms-win-crt-conio-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-crt-heap-l1-1-0.dll',
   'C:\\WINDOWS\\system32\\api-ms-win-crt-heap-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-crt-convert-l1-1-0.dll',
   'C:\\WINDOWS\\system32\\api-ms-win-crt-convert-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-crt-runtime-l1-1-0.dll',
   'C:\\WINDOWS\\system32\\api-ms-win-crt-runtime-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-crt-locale-l1-1-0.dll',
   'C:\\WINDOWS\\system32\\api-ms-win-crt-locale-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-crt-environment-l1-1-0.dll',
   'C:\\WINDOWS\\system32\\api-ms-win-crt-environment-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-crt-filesystem-l1-1-0.dll',
   'C:\\WINDOWS\\system32\\api-ms-win-crt-filesystem-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-crt-string-l1-1-0.dll',
   'C:\\WINDOWS\\system32\\api-ms-win-crt-string-l1-1-0.dll',
   'BINARY'),
  ('libcrypto-3.dll', 'C:\\Python312\\DLLs\\libcrypto-3.dll', 'BINARY'),
  ('api-ms-win-crt-utility-l1-1-0.dll',
   'C:\\WINDOWS\\system32\\api-ms-win-crt-utility-l1-1-0.dll',
   'BINARY'),
  ('libssl-3.dll', 'C:\\Python312\\DLLs\\libssl-3.dll', 'BINARY'),
  ('libffi-8.dll', 'C:\\Python312\\DLLs\\libffi-8.dll', 'BINARY'),
  ('sqlite3.dll', 'C:\\Python312\\DLLs\\sqlite3.dll', 'BINARY'),
  ('VCRUNTIME140_1.dll', 'C:\\Python312\\VCRUNTIME140_1.dll', 'BINARY'),
  ('ucrtbase.dll', 'C:\\WINDOWS\\system32\\ucrtbase.dll', 'BINARY'),
  ('api-ms-win-core-util-l1-1-0.dll',
   'C:\\WINDOWS\\system32\\api-ms-win-core-util-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-core-string-l1-1-0.dll',
   'C:\\WINDOWS\\system32\\api-ms-win-core-string-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-core-interlocked-l1-1-0.dll',
   'C:\\WINDOWS\\system32\\api-ms-win-core-interlocked-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-core-synch-l1-1-0.dll',
   'C:\\WINDOWS\\system32\\api-ms-win-core-synch-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-core-synch-l1-2-0.dll',
   'C:\\WINDOWS\\system32\\api-ms-win-core-synch-l1-2-0.dll',
   'BINARY'),
  ('api-ms-win-core-debug-l1-1-0.dll',
   'C:\\WINDOWS\\system32\\api-ms-win-core-debug-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-core-namedpipe-l1-1-0.dll',
   'C:\\WINDOWS\\system32\\api-ms-win-core-namedpipe-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-core-datetime-l1-1-0.dll',
   'C:\\WINDOWS\\system32\\api-ms-win-core-datetime-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-core-sysinfo-l1-1-0.dll',
   'C:\\WINDOWS\\system32\\api-ms-win-core-sysinfo-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-core-fibers-l1-1-0.dll',
   'C:\\Program Files (x86)\\Windows Kits\\10\\Windows Performance '
   'Toolkit\\api-ms-win-core-fibers-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-core-timezone-l1-1-0.dll',
   'C:\\WINDOWS\\system32\\api-ms-win-core-timezone-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-core-handle-l1-1-0.dll',
   'C:\\WINDOWS\\system32\\api-ms-win-core-handle-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-core-heap-l1-1-0.dll',
   'C:\\WINDOWS\\system32\\api-ms-win-core-heap-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-core-libraryloader-l1-1-0.dll',
   'C:\\WINDOWS\\system32\\api-ms-win-core-libraryloader-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-core-file-l2-1-0.dll',
   'C:\\WINDOWS\\system32\\api-ms-win-core-file-l2-1-0.dll',
   'BINARY'),
  ('api-ms-win-core-processthreads-l1-1-1.dll',
   'C:\\WINDOWS\\system32\\api-ms-win-core-processthreads-l1-1-1.dll',
   'BINARY'),
  ('api-ms-win-core-file-l1-1-0.dll',
   'C:\\WINDOWS\\system32\\api-ms-win-core-file-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-core-profile-l1-1-0.dll',
   'C:\\WINDOWS\\system32\\api-ms-win-core-profile-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-core-memory-l1-1-0.dll',
   'C:\\WINDOWS\\system32\\api-ms-win-core-memory-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-core-file-l1-2-0.dll',
   'C:\\WINDOWS\\system32\\api-ms-win-core-file-l1-2-0.dll',
   'BINARY'),
  ('api-ms-win-core-rtlsupport-l1-1-0.dll',
   'C:\\WINDOWS\\system32\\api-ms-win-core-rtlsupport-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-core-processenvironment-l1-1-0.dll',
   'C:\\WINDOWS\\system32\\api-ms-win-core-processenvironment-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-core-errorhandling-l1-1-0.dll',
   'C:\\WINDOWS\\system32\\api-ms-win-core-errorhandling-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-core-processthreads-l1-1-0.dll',
   'C:\\WINDOWS\\system32\\api-ms-win-core-processthreads-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-core-localization-l1-2-0.dll',
   'C:\\WINDOWS\\system32\\api-ms-win-core-localization-l1-2-0.dll',
   'BINARY'),
  ('api-ms-win-core-console-l1-1-0.dll',
   'C:\\WINDOWS\\system32\\api-ms-win-core-console-l1-1-0.dll',
   'BINARY'),
  ('base_library.zip',
   'E:\\github\\local_upload\\backend\\build\\backend\\base_library.zip',
   'DATA'),
  ('importlib_metadata-8.0.0.dist-info\\top_level.txt',
   'E:\\github\\local_upload\\venv\\Lib\\site-packages\\setuptools\\_vendor\\importlib_metadata-8.0.0.dist-info\\top_level.txt',
   'DATA'),
  ('importlib_metadata-8.0.0.dist-info\\RECORD',
   'E:\\github\\local_upload\\venv\\Lib\\site-packages\\setuptools\\_vendor\\importlib_metadata-8.0.0.dist-info\\RECORD',
   'DATA'),
  ('importlib_metadata-8.0.0.dist-info\\WHEEL',
   'E:\\github\\local_upload\\venv\\Lib\\site-packages\\setuptools\\_vendor\\importlib_metadata-8.0.0.dist-info\\WHEEL',
   'DATA'),
  ('importlib_metadata-8.0.0.dist-info\\INSTALLER',
   'E:\\github\\local_upload\\venv\\Lib\\site-packages\\setuptools\\_vendor\\importlib_metadata-8.0.0.dist-info\\INSTALLER',
   'DATA'),
  ('importlib_metadata-8.0.0.dist-info\\METADATA',
   'E:\\github\\local_upload\\venv\\Lib\\site-packages\\setuptools\\_vendor\\importlib_metadata-8.0.0.dist-info\\METADATA',
   'DATA'),
  ('importlib_metadata-8.0.0.dist-info\\REQUESTED',
   'E:\\github\\local_upload\\venv\\Lib\\site-packages\\setuptools\\_vendor\\importlib_metadata-8.0.0.dist-info\\REQUESTED',
   'DATA'),
  ('importlib_metadata-8.0.0.dist-info\\LICENSE',
   'E:\\github\\local_upload\\venv\\Lib\\site-packages\\setuptools\\_vendor\\importlib_metadata-8.0.0.dist-info\\LICENSE',
   'DATA'),
  ('itsdangerous-2.2.0.dist-info\\INSTALLER',
   'E:\\github\\local_upload\\venv\\Lib\\site-packages\\itsdangerous-2.2.0.dist-info\\INSTALLER',
   'DATA'),
  ('werkzeug-3.1.3.dist-info\\METADATA',
   'E:\\github\\local_upload\\venv\\Lib\\site-packages\\werkzeug-3.1.3.dist-info\\METADATA',
   'DATA'),
  ('itsdangerous-2.2.0.dist-info\\LICENSE.txt',
   'E:\\github\\local_upload\\venv\\Lib\\site-packages\\itsdangerous-2.2.0.dist-info\\LICENSE.txt',
   'DATA'),
  ('click-8.2.1.dist-info\\licenses\\LICENSE.txt',
   'E:\\github\\local_upload\\venv\\Lib\\site-packages\\click-8.2.1.dist-info\\licenses\\LICENSE.txt',
   'DATA'),
  ('werkzeug-3.1.3.dist-info\\INSTALLER',
   'E:\\github\\local_upload\\venv\\Lib\\site-packages\\werkzeug-3.1.3.dist-info\\INSTALLER',
   'DATA'),
  ('werkzeug-3.1.3.dist-info\\LICENSE.txt',
   'E:\\github\\local_upload\\venv\\Lib\\site-packages\\werkzeug-3.1.3.dist-info\\LICENSE.txt',
   'DATA'),
  ('flask-2.3.3.dist-info\\INSTALLER',
   'E:\\github\\local_upload\\venv\\Lib\\site-packages\\flask-2.3.3.dist-info\\INSTALLER',
   'DATA'),
  ('MarkupSafe-3.0.2.dist-info\\LICENSE.txt',
   'E:\\github\\local_upload\\venv\\Lib\\site-packages\\MarkupSafe-3.0.2.dist-info\\LICENSE.txt',
   'DATA'),
  ('click-8.2.1.dist-info\\INSTALLER',
   'E:\\github\\local_upload\\venv\\Lib\\site-packages\\click-8.2.1.dist-info\\INSTALLER',
   'DATA'),
  ('MarkupSafe-3.0.2.dist-info\\WHEEL',
   'E:\\github\\local_upload\\venv\\Lib\\site-packages\\MarkupSafe-3.0.2.dist-info\\WHEEL',
   'DATA'),
  ('MarkupSafe-3.0.2.dist-info\\top_level.txt',
   'E:\\github\\local_upload\\venv\\Lib\\site-packages\\MarkupSafe-3.0.2.dist-info\\top_level.txt',
   'DATA'),
  ('click-8.2.1.dist-info\\RECORD',
   'E:\\github\\local_upload\\venv\\Lib\\site-packages\\click-8.2.1.dist-info\\RECORD',
   'DATA'),
  ('flask-2.3.3.dist-info\\REQUESTED',
   'E:\\github\\local_upload\\venv\\Lib\\site-packages\\flask-2.3.3.dist-info\\REQUESTED',
   'DATA'),
  ('MarkupSafe-3.0.2.dist-info\\INSTALLER',
   'E:\\github\\local_upload\\venv\\Lib\\site-packages\\MarkupSafe-3.0.2.dist-info\\INSTALLER',
   'DATA'),
  ('click-8.2.1.dist-info\\METADATA',
   'E:\\github\\local_upload\\venv\\Lib\\site-packages\\click-8.2.1.dist-info\\METADATA',
   'DATA'),
  ('itsdangerous-2.2.0.dist-info\\RECORD',
   'E:\\github\\local_upload\\venv\\Lib\\site-packages\\itsdangerous-2.2.0.dist-info\\RECORD',
   'DATA'),
  ('MarkupSafe-3.0.2.dist-info\\RECORD',
   'E:\\github\\local_upload\\venv\\Lib\\site-packages\\MarkupSafe-3.0.2.dist-info\\RECORD',
   'DATA'),
  ('flask-2.3.3.dist-info\\WHEEL',
   'E:\\github\\local_upload\\venv\\Lib\\site-packages\\flask-2.3.3.dist-info\\WHEEL',
   'DATA'),
  ('werkzeug-3.1.3.dist-info\\RECORD',
   'E:\\github\\local_upload\\venv\\Lib\\site-packages\\werkzeug-3.1.3.dist-info\\RECORD',
   'DATA'),
  ('itsdangerous-2.2.0.dist-info\\METADATA',
   'E:\\github\\local_upload\\venv\\Lib\\site-packages\\itsdangerous-2.2.0.dist-info\\METADATA',
   'DATA'),
  ('MarkupSafe-3.0.2.dist-info\\METADATA',
   'E:\\github\\local_upload\\venv\\Lib\\site-packages\\MarkupSafe-3.0.2.dist-info\\METADATA',
   'DATA'),
  ('werkzeug-3.1.3.dist-info\\WHEEL',
   'E:\\github\\local_upload\\venv\\Lib\\site-packages\\werkzeug-3.1.3.dist-info\\WHEEL',
   'DATA'),
  ('itsdangerous-2.2.0.dist-info\\WHEEL',
   'E:\\github\\local_upload\\venv\\Lib\\site-packages\\itsdangerous-2.2.0.dist-info\\WHEEL',
   'DATA'),
  ('flask-2.3.3.dist-info\\entry_points.txt',
   'E:\\github\\local_upload\\venv\\Lib\\site-packages\\flask-2.3.3.dist-info\\entry_points.txt',
   'DATA'),
  ('click-8.2.1.dist-info\\WHEEL',
   'E:\\github\\local_upload\\venv\\Lib\\site-packages\\click-8.2.1.dist-info\\WHEEL',
   'DATA'),
  ('flask-2.3.3.dist-info\\RECORD',
   'E:\\github\\local_upload\\venv\\Lib\\site-packages\\flask-2.3.3.dist-info\\RECORD',
   'DATA'),
  ('flask-2.3.3.dist-info\\METADATA',
   'E:\\github\\local_upload\\venv\\Lib\\site-packages\\flask-2.3.3.dist-info\\METADATA',
   'DATA'),
  ('flask-2.3.3.dist-info\\LICENSE.rst',
   'E:\\github\\local_upload\\venv\\Lib\\site-packages\\flask-2.3.3.dist-info\\LICENSE.rst',
   'DATA')],)
